
package com.heating.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.heating.dto.order.WorkOrderBaseInfoResponse;
import com.heating.dto.order.WorkOrderDetailResponse;
import com.heating.entity.fault.TFaultAttachment;
import com.heating.entity.order.TOperationLog;
import com.heating.entity.order.TWorkOrder;
import com.heating.entity.order.TWorkOrderAttachment;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
@Repository
public interface WorkOrderRepository extends JpaRepository<TWorkOrder, Long> {

    @Query(value =
        "SELECT " +
            "wo.id as orderId, " +
            "wo.orderNo as orderNo, " +
            "h.name as heatUnitName, " +
            "f.faultType as faultType, " + 
            "f.faultLevel as faultLevel, " +
            "wo.orderStatus as orderStatus, " +
            "DATE_FORMAT(wo.createdAt, '%Y-%m-%d %H:%i') as createdTime " +
        "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON f.id = wo.fault_id " +
            "LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id " +
        "WHERE 1=1 " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:status IS NULL OR wo.order_status = :status) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId) " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
        "ORDER BY wo.created_at DESC", nativeQuery = true)   
    List<Map<String, Object>> findWorkOrderList(
        @Param("date") Date date, 
        @Param("status") String status,
        @Param("userId") Long userId,
        @Param("heatUnitId") String heatUnitId);

    @Query(value =
            "SELECT " +
                    "   wo.id AS orderId, " +
                    "   wo.order_no AS orderNo, " +
                    "   h.name AS heatUnitName, " +
                    "   f.fault_type AS faultType, " +
                    "   f.fault_level AS faultLevel, " +
                    "   wo.order_status AS orderStatus, " +
                    "   wo.repair_user_id AS repairUserId, " +
                    "   wo.transfer_user_id AS transferUserId, " +
                    "   DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') AS createdTime " +
                    "FROM t_work_order wo " +
                    "LEFT JOIN t_fault f ON f.id = wo.fault_id " +
                    "LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id " +
                    "WHERE 1 = 1 " +
                    "AND (:status IS NULL OR wo.order_status = :status) " +
                    "AND (" +
                    "   :heatUnitId IS NULL OR " +
                    "   :heatUnitId = '0' OR " +
                    "   FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), REPLACE(:heatUnitId, ' ', '')) > 0" +
                    ") " +
                    "ORDER BY wo.created_at DESC " +
                    "LIMIT :limit",
            nativeQuery = true)
    List<Map<String, Object>> findWorkOrderListWithLimit(
            @Param("status") String status,
            @Param("limit") Integer limit,
            @Param("heatUnitId") String heatUnitId);

    @Query(value =
        "SELECT " + 
            "wo.order_no as orderNo, " +
            "wo.fault_id as faultId, " +
            "h.name as heatUnitName, " +
            "wo.repair_user_id as repairUserId, " +
            "u.name as repairUserName, " +
            "wo.transfer_user_id as transferUserId, " +
            "tu.name as transferUserName, " +
            "wo.transfer_reason as transferReason, " +
            "DATE_FORMAT(wo.transfer_time, '%Y-%m-%d %H:%i') as transferTime, " +
            "wo.repair_content as repairContent, " +
            "wo.repair_result as repairResult, " +
            "wo.order_status as orderStatus, " + 
            "f.fault_type as faultType, " + 
            "f.fault_level as faultLevel, " +
            "f.address as address, " +
            "f.fault_desc as faultDesc, " +
            "DATE_FORMAT(wo.repair_time, '%Y-%m-%d %H:%i') as repairTime, " +
            "wo.repair_materials_quantity as repairMaterialsQuantity, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime, " +
            "DATE_FORMAT(wo.updated_at, '%Y-%m-%d %H:%i') as updatedTime " +
        "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +  
            "LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id " +
            "LEFT JOIN t_user_app u ON wo.repair_user_id = u.id " +
            "LEFT JOIN t_user_app tu ON wo.transfer_user_id = tu.id " +
        "WHERE wo.id = :orderId", nativeQuery = true)
    Optional<Map<String, Object>> findWorkOrderDetail(@Param("orderId") long orderId);

    @Query(value =
        "SELECT * FROM t_work_order_attachment wa " +
        "WHERE wa.work_order_id = :orderId " +
        "ORDER BY wa.created_at DESC", nativeQuery = true)
    List<TWorkOrderAttachment> findWorkOrderAttachments(@Param("orderId") long orderId);

    @Query(value =
        "SELECT fa.* FROM t_fault_attachment fa " +
            "LEFT JOIN t_work_order wo ON fa.fault_id = wo.fault_id " +
        "WHERE wo.id = :orderId " +
        "ORDER BY fa.created_at DESC", nativeQuery = true)
    List<TFaultAttachment> findFaultAttachments(@Param("orderId") long orderId);

    @Query(value =
            "SELECT ol.id, ol.work_order_id as workOrderId, " +
            "ol.operation_type as operationType, " +
            "ol.operation_desc as operationDesc, " +
            "ol.operator_id as operatorId, " +
            "ol.created_at as createdAt, " +
            "u.name as operatorName " +
            "FROM t_operation_log ol " +
            "LEFT JOIN t_user_app u ON ol.operator_id = u.id " +
            "WHERE ol.work_order_id = :orderId " +
            "ORDER BY ol.created_at DESC", nativeQuery = true)
    List<Map<String, Object>> findOperationLogs(@Param("orderId") long orderId);

    @Query(value =
        "SELECT " +
            "wo.order_status as status, " +
            "COUNT(*) as count " +
        "FROM t_work_order wo " +
        "WHERE (:date IS NULL OR DATE(wo.created_at) = :date) " +
        "GROUP BY wo.order_status", nativeQuery = true)
    List<Map<String, Object>> countWorkOrdersByStatus(@Param("date") LocalDate date);

    Optional<TWorkOrder> findByOrderNo(String orderNo);

    Optional<TWorkOrder> findByFaultId(long faultId);

    /**
     * 根据工单状态查询工单信息
     * 从工单信息表中，获取状态为指定状态（如'待接单'）的记录列表
     * @param status 工单状态
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 符合条件的工单列表
     */
    @Query(value =
        "SELECT " +
            "wo.id as id, " +
            "wo.order_no as orderNo, " +
            "f.fault_source as faultSource, " +
            "f.fault_desc as faultDesc, " +
            "hu.name as heatUnitName, " +
            "wo.created_at as createdTime, " +
            "wo.updated_at as updatedTime " +
        "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
        "WHERE wo.order_status = :status " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
        "ORDER BY wo.created_at DESC", nativeQuery = true)
    List<Map<String, Object>> findWorkOrdersByStatus(
        @Param("status") String status,
        @Param("heatUnitId") String heatUnitId);

    /**
     * 根据工单状态和用户ID查询工单信息
     * 从工单信息表中，获取状态为指定状态（如'待接单'）且维修人员ID为指定值的记录列表
     * @param status 工单状态
     * @param userId 用户ID
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 符合条件的工单列表
     */
    @Query(value =
        "SELECT " +
            "wo.id as id, " +
            "wo.order_no as orderNo, " +
            "f.fault_source as faultSource, " +
            "f.fault_desc as faultDesc, " +
            "wo.created_at as createdTime, " +
            "wo.updated_at as updatedTime " +
        "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
        "WHERE wo.order_status = :status " +
            "AND wo.repair_user_id = :userId " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
        "ORDER BY wo.created_at DESC", nativeQuery = true)
    List<Map<String, Object>> findWorkOrdersByStatusAndUserId(
        @Param("status") String status, 
        @Param("userId") Long userId,
        @Param("heatUnitId") String heatUnitId);

    /**
     * 分页查询工单列表
     */
    @Query(value = "SELECT " +
            "wo.id as orderId, " +
            "wo.order_no as orderNo, " +
            "hu.name as heatUnitName, " +
            "f.fault_type as faultType, " +
            "f.fault_level as faultLevel, " +
            "wo.order_status as orderStatus, " +
            "wo.repair_user_id as repairUserId, " +
            "wo.transfer_user_id as transferUserId, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId) " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
            "ORDER BY wo.created_at DESC " +
            "LIMIT :offset, :pageSize", nativeQuery = true)
    List<Map<String, Object>> findWorkOrderListWithPaging(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("heatUnitId") String heatUnitId);

    /**
     * 统计工单总数
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId) " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0')", nativeQuery = true)
    long countWorkOrderList(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("heatUnitId") String heatUnitId);

    /**
     * 分页查询工单列表（包含转派信息）
     * 查询条件：用户ID匹配repair_user_id或transfer_user_id
     */
    @Query(value = "SELECT " +
            "wo.id as orderId, " +
            "wo.order_no as orderNo, " +
            "hu.name as heatUnitName, " +
            "f.fault_type as faultType, " +
            "f.fault_level as faultLevel, " +
            "wo.order_status as orderStatus, " +
            "wo.repair_user_id as repairUserId, " +
            "wo.transfer_user_id as transferUserId, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
            "ORDER BY wo.created_at DESC " +
            "LIMIT :offset, :pageSize", nativeQuery = true)
    List<Map<String, Object>> findWorkOrderListWithTransferInfo(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("heatUnitId") String heatUnitId);
            
    /**
     * 统计工单总数（包含转派信息）
     * 计数条件：用户ID匹配repair_user_id或transfer_user_id
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0')", nativeQuery = true)
    long countWorkOrderListWithTransferInfo(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("heatUnitId") String heatUnitId);
            
    /**
     * 查询我的工单列表（分页）
     * 查询条件：用户ID匹配repair_user_id（我负责的工单）
     */
    @Query(value = "SELECT " +
            "wo.id as orderId, " +
            "wo.order_no as orderNo, " +
            "hu.name as heatUnitName, " +
            "f.fault_type as faultType, " +
            "f.fault_level as faultLevel, " +
            "wo.order_status as orderStatus, " +
            "wo.repair_user_id as repairUserId, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND wo.repair_user_id = :userId " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
            "ORDER BY wo.created_at DESC " +
            "LIMIT :offset, :pageSize", nativeQuery = true)
    List<Map<String, Object>> findMyWorkOrdersWithPaging(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("heatUnitId") String heatUnitId);
            
    /**
     * 统计我的工单总数
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND wo.repair_user_id = :userId " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0')", nativeQuery = true)
    long countMyWorkOrders(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("heatUnitId") String heatUnitId);
            
    /**
     * 查询我的工单列表（限制数量）
     */
    @Query(value = "SELECT " +
            "wo.id as orderId, " +
            "wo.order_no as orderNo, " +
            "hu.name as heatUnitName, " +
            "f.fault_type as faultType, " +
            "f.fault_level as faultLevel, " +
            "wo.order_status as orderStatus, " +
            "wo.repair_user_id as repairUserId, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "WHERE (:status IS NULL OR wo.order_status = :status) " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND wo.repair_user_id = :userId " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
            "ORDER BY wo.created_at DESC " +
            "LIMIT :limit", nativeQuery = true)
    List<Map<String, Object>> findMyWorkOrdersWithLimit(
            @Param("date") Date date,
            @Param("status") String status,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("limit") Integer limit,
            @Param("heatUnitId") String heatUnitId);
            
    /**
     * 查询待接单工单列表（分页）
     * 查询条件：工单状态为"待接单"
     */
    @Query(value = "SELECT " +
            "wo.id as orderId, " +
            "wo.order_no as orderNo, " +
            "hu.name as heatUnitName, " +
            "f.fault_type as faultType, " +
            "f.fault_level as faultLevel, " +
            "wo.order_status as orderStatus, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "WHERE wo.order_status = '待接单' " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR 1=1) " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
            "ORDER BY wo.created_at DESC " +
            "LIMIT :offset, :pageSize", nativeQuery = true)
    List<Map<String, Object>> findPendingWorkOrdersWithPaging(
            @Param("date") Date date,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize,
            @Param("heatUnitId") String heatUnitId);
            
    /**
     * 统计待接单工单总数
     * @param date 日期过滤
     * @param userId 用户ID过滤，如果是管理员或主管角色，此参数可以为null
     * @param orderNo 工单号过滤
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @param isAdmin 是否是管理员或主管角色，如果是，则忽略userId参数
     * @return 待接单工单总数
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "WHERE wo.order_status = '待接单' " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:isAdmin = true OR :userId IS NULL OR 1=1) " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0')", nativeQuery = true)
    long countPendingWorkOrders(
            @Param("date") Date date,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("heatUnitId") String heatUnitId,
            @Param("isAdmin") Boolean isAdmin);
            
    /**
     * 查询待接单工单列表（限制数量）
     */
    @Query(value = "SELECT " +
            "wo.id as orderId, " +
            "wo.order_no as orderNo, " +
            "hu.name as heatUnitName, " +
            "f.fault_type as faultType, " +
            "f.fault_level as faultLevel, " +
            "wo.order_status as orderStatus, " +
            "DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "LEFT JOIN t_heat_unit hu ON f.heat_unit_id = hu.id " +
            "WHERE wo.order_status = '待接单' " +
            "AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) " +
            "AND (:date IS NULL OR DATE(wo.created_at) = :date) " +
            "AND (:userId IS NULL OR 1=1) " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0') " +
            "ORDER BY wo.created_at DESC " +
            "LIMIT :limit", nativeQuery = true)
    List<Map<String, Object>> findPendingWorkOrdersWithLimit(
            @Param("date") Date date,
            @Param("userId") Long userId,
            @Param("orderNo") String orderNo,
            @Param("limit") Integer limit,
            @Param("heatUnitId") String heatUnitId);
            
    /**
     * 获取用户角色
     * @param userId 用户ID
     * @return 用户角色
     */
    @Query(value = "SELECT role FROM t_user_app WHERE id = :userId", nativeQuery = true)
    String getUserRole(@Param("userId") Long userId);
    
    /**
     * 统计所有处理中工单数量（管理员和主管用）
     * 统计条件：工单状态为"处理中"且在用户项目权限范围内
     * @param heatUnitId 用户项目权限，逗号分隔的项目ID字符串
     * @return 处理中的工单数量
     */
    @Query(value = "SELECT COUNT(*) " +
            "FROM t_work_order wo " +
            "LEFT JOIN t_fault f ON wo.fault_id = f.id " +
            "WHERE wo.order_status = '处理中' " +
            "AND (:heatUnitId IS NULL OR " +
            "    FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR " +
            "    :heatUnitId = '0')", nativeQuery = true)
    long countAllProcessingWorkOrders(@Param("heatUnitId") String heatUnitId);
}
