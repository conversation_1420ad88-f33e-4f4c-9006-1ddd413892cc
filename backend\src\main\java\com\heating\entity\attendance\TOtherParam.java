package com.heating.entity.attendance;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@Entity
@Table(name = "t_other_param")
public class TOtherParam {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "price")
    private String price; // 供热单价

    @Column(name = "rated_temp")
    private String ratedTemp; // 额定温度

    @Column(name = "collect_cycle")
    private String collectCycle; // 阀门热表采集周期，单位分钟

    @Column(name = "indoor_cycle")
    private String indoorCycle; // 室内面板采集周期

    @Column(name = "heatmater_cycle")
    private String heatmaterCycle; // 热表采集周期

    @Column(name = "alarm_cycle")
    private Integer alarmCycle; // 告警周期

    @Column(name = "position_cycle")
    private Integer positionCycle; // 定位上报周期 分钟

    @Column(name = "last_alarm_date")
    private LocalDateTime lastAlarmDate;

    @Column(name = "clock_in_time")
    private LocalTime clockInTime; // 上班打卡时间

    @Column(name = "clock_out_time")
    private LocalTime clockOutTime; // 下班打卡时间

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "create_user")
    private Integer createUser;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @Column(name = "update_user")
    private Integer updateUser;

    @Column(name = "mark")
    private Boolean mark;
}