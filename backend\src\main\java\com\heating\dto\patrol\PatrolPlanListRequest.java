package com.heating.dto.patrol;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
/**
 * 巡检计划列表请求DTO
 */
@Data
public class PatrolPlanListRequest {
    /**
     * 巡检名称
     */
    private String name;
    
    /**
     * 巡检状态: pending-待执行,processing-执行中,completed-已完成
     */
    private String status;
    
    /**
     * 巡检类型: 换热站巡检，日常巡检，设备巡检，管道巡检，阀门巡检
     */
    @JsonProperty("patrol_type")
    private String patrolType;
    
    /**
     * 检索巡检时间: 今天，本周，本月，上个月
     */
    @JsonProperty("search_date")
    private String searchDate;
    
    /**
     * 巡检地点
     */
    private String locations;

    /**
     * 项目权限
     */
    private String heatUnitId;
    
    private Integer page = 1; // 默认第1页

    private Integer pageSize = 10; // 默认每页10条
} 