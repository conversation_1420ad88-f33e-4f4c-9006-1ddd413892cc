// API配置文件
//const BASE_URL = 'http://43.139.65.175:8889';
const BASE_URL = 'http://127.0.0.1:8889'; // 修改为公网地址
const TIMEOUT = 10000; // 10秒超时
const RETRY_TIMES = 2; // 重试次数

// 测试API连接
function testApiConnection() {
  return new Promise((resolve, reject) => {
    console.log('正在测试API连接:', BASE_URL);
    
    // 改为测试登录接口，而不是根路径
    uni.request({
      url: `${BASE_URL}/api/auth/login`,
      method: 'OPTIONS', // 使用OPTIONS请求检查CORS预检请求
      timeout: 5000,
      // 禁用withCredentials，解决CORS问题
      withCredentials: false,
      header: {
        'Content-Type': 'application/json',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'content-type,authorization'
      },
      success: (res) => {
        console.log('API连接测试成功:', res);
        // 只要能收到响应，不管状态码是什么，都认为API可连接
        resolve(true);
      },
      fail: (err) => {
        console.error('API连接测试失败:', err);
        resolve(false);
      }
    });
  });
}

// 封装请求函数
function request(options) {
  let retryCount = 0;
  
  const executeRequest = () => {
    return new Promise((resolve, reject) => {
      // 完整的URL处理，确保URL正确拼接
      const url = options.url.startsWith('http') ? options.url : BASE_URL + options.url;
      
      // 输出请求日志
      //console.log(`Request URL (attempt ${retryCount + 1}):`, url);
      //console.log('Request Method:', options.method || 'GET');
      //console.log('Request Data:', options.data);
      
      // 准备请求头
      const header = {
        'Content-Type': 'application/json',
        ...(options.header || {})
      };
      
      // 添加token (如果有)
      const token = uni.getStorageSync('token');
      if (token) {
        header.Authorization = `Bearer ${token}`;
        //console.log('Using token:', token);
      } else {
        console.warn('No token found in storage');
      }
      
      // 发起请求
      uni.request({
        url: url,
        method: options.method || 'GET',
        data: options.data,
        header: header,
        // 超时时间设置
        timeout: options.timeout || TIMEOUT,
        success: (res) => {
          console.log('Response:', res);
          
          // 检查认证相关错误
          if (res.statusCode === 401) {
            console.error('认证失败，需要重新登录');
            // 清除本地token
            uni.removeStorageSync('token');
            // 跳转到登录页
            uni.redirectTo({
              url: '/pages/user/login'
            });
            reject({
              errMsg: '认证失败，请重新登录',
              statusCode: 401
            });
            return;
          }
          
          // 检查其他HTTP状态码 - 将2xx状态码都视为成功
          if (res.statusCode >= 200 && res.statusCode < 300) {
            // 2xx 状态码，表示请求成功
            resolve(res.data);
          } else {
            // 处理非2xx状态码
            console.error('API错误:', res.statusCode, res.data);
            reject({
              errMsg: `请求失败: ${res.statusCode}`,
              statusCode: res.statusCode,
              data: res.data
            });
          }
        },
        fail: async (err) => {
          console.error(`请求失败 (attempt ${retryCount + 1}):`, err);
          
          // 如果是网络错误且未超过重试次数，则重试
          if (retryCount < RETRY_TIMES && 
              (err.errMsg.includes('timeout') || err.errMsg.includes('network'))) {
            retryCount++;
            console.log(`正在重试 (${retryCount}/${RETRY_TIMES})...`);
            try {
              const result = await executeRequest();
              resolve(result);
            } catch (retryError) {
              reject(retryError);
            }
          } else {
            reject({
              errMsg: err.errMsg || '网络请求失败',
              url: url,
              method: options.method || 'GET',
              data: options.data
            });
          }
        }
      });
    });
  };
  
  return executeRequest();
}

// 材料管理API
const materialsApi = {
  /**
   * 获取材料列表
   * @returns {Promise} 包含材料列表数据的Promise对象
   */
  getMaterialsList() {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/materials/list',
        method: 'GET'
      })
      .then(res => {
        console.log('获取材料列表成功:', res);
        resolve(res);
      })
      .catch(err => {
        console.warn('获取材料列表失败，使用默认数据', err);
        // 提供默认数据，避免页面错误
        resolve({
          code: 200,
          message: "材料列表查询成功(本地数据)",
          data: [
            { id: 1, name: '铜管', material_type: '管材' },
            { id: 2, name: '不锈钢管', material_type: '管材' },
            { id: 3, name: '橡胶密封圈', material_type: '密封件' },
            { id: 4, name: '球阀', material_type: '阀门' },
            { id: 5, name: '闸阀', material_type: '阀门' },
            { id: 6, name: '法兰', material_type: '连接件' },
            { id: 7, name: '螺栓', material_type: '连接件' },
            { id: 8, name: '防水胶带', material_type: '辅材' },
            { id: 9, name: '保温材料', material_type: '辅材' },
            { id: 10, name: '电线', material_type: '电气' }
          ]
        });
      });
    });
  }
};

// 用户模块API
const userApi = {
  login(data) {
    console.log('尝试登录，数据:', data);
    
    // 确保数据格式正确
    const loginData = {
      username: data.username,
      password: data.password
    };
    
    return new Promise((resolve, reject) => {
      uni.request({
        url: `${BASE_URL}/api/auth/login`,
        method: 'POST',
        data: loginData,
        header: {
          'Content-Type': 'application/json'
        },
        withCredentials: true,
        success: (res) => {
          console.log('登录响应:', res);
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            // 处理非200状态码
            console.error('登录错误:', res.statusCode, res.data);
            reject({
              errMsg: `登录失败: ${res.statusCode}`,
              statusCode: res.statusCode,
              data: res.data
            });
          }
        },
        fail: (err) => {
          console.error('登录请求异常:', err);
          reject({
            errMsg: err.errMsg || '网络请求失败',
            url: `${BASE_URL}/api/auth/login`,
            method: 'POST',
            data: loginData
          });
        }
      });
    });
  },
  getUserInfo() {
    return request({
      url: '/api/auth/user-info',
      method: 'GET'
    });
  },
  getInspectorList(role) {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/auth/user/list',
        method: 'POST',
		data:role
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取巡检人员列表失败，使用默认数据', err);
        // 提供默认数据，避免页面错误
        resolve({
          code: 200,
          message: "获取人员列表成功(本地数据)",
          data: [
          ]
        });
      });
    });
  },
  updateUserInfo(data) {
    return request({
      url: '/api/auth/user-modify',
      method: 'POST',
      data
    });
  },
  /**
   * 修改用户密码
   * @param {Object} data - 包含当前密码和新密码的对象
   * @returns {Promise} 包含修改结果的Promise对象
   */
  changePassword(data) {
    return request({
      url: '/api/auth/change-password',
      method: 'POST',
      data
    });
  },
  /**
   * 获取系统权限列表
   * 根据后端接口文档 1.6 获取系统权限列表
   * @returns {Promise} 包含权限列表数据的Promise对象
   */
  getPermissionList() {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/system/permission',
        method: 'GET'
      })
      .then(res => {
        console.log('获取权限列表成功:', res);
        resolve(res);
      })
      .catch(err => {
        console.warn('获取权限列表失败:', err); 
      });
    });
  }
};

// 工单模块API
const workOrderApi = {
  getList(params) {
    // 将前端参数转换为后端API所需格式
    const apiParams = {
      page: params?.page || 1,
      pageSize: params?.pageSize || 10
    };
    
    // 处理日期参数
    if (params && params.date) {
      apiParams.date = params.date;
    }
    
    // 处理状态参数
    if (params && params.status) {
      apiParams.status = params.status;
    }
    
    // 处理用户ID参数
    apiParams.uid = params?.uid || uni.getStorageSync("userId");
    
    // 处理工单类型参数
    if (params && params.type) {
      apiParams.type = params.type;
    }
    
    // 处理用户项目权限参数
    apiParams.heatUnitId = params?.heatUnitId || uni.getStorageSync("heatUnitId");
    
    console.log('工单列表请求参数:', apiParams);
    return request({
      url: '/api/WorkOrders/list',
      method: 'GET',
      data: apiParams
    });
  },
  getDetail(id) {
    return request({
      url: `/api/WorkOrders/detail/${id}`,
      method: 'GET'
    });
  },
  updateStatus(data) {
    return request({
      url: '/api/WorkOrders/status',
      method: 'POST',
      data
    });
  },
  completeOrder(data) {
    return request({
      url: '/api/WorkOrders/complete',
      method: 'POST',
      data
    });
  },
  transferOrder(data) {
    return request({
      url: '/api/WorkOrders/transfer',
      method: 'POST',
      data
    });
  },
  // 工单消息 /api/WorkOrders/messages
  getWorkOrderMessages(userId, userRole) {
    return request({
      url: '/api/WorkOrders/messages',
      method: 'GET',
      data: {
        uid: userId,
        role: userRole,
        heatUnitId: uni.getStorageSync("heatUnitId")
      }
    });
  },
  // 获取工单统计数据
  getWorkOrderStats() {
    const apiParams = {};
    apiParams.uid = uni.getStorageSync("userId");
    apiParams.heatUnitId = uni.getStorageSync("heatUnitId");
    
    return request({
      url: '/api/WorkOrders/stats',
      method: 'GET',
      data: apiParams
    });
  }
};

// 巡检模块API
const patrolApi = {
  // 创建巡检计划
  createPlan(data) {
    return request({
      url: '/api/patrols/plans',
      method: 'POST',
      data
    });
  },
  
  // 获取巡检计划列表
  getPlanList(params) {
    return new Promise((resolve, reject) => {
		
      request({
        url: '/api/patrols/plans/list',
        method: 'POST',
        data: {
          name: params?.name || null,
          status: params?.status || null,
          patrol_type: params?.type || null,
          search_date: params?.timeRange || null,
          locations: params?.locations || null,
		  page: params?.page || 1,
		  pageSize: params?.pageSize || 10,
		  heatUnitId:params.heatUnitId,
        }
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取巡检计划列表失败，使用默认数据', err); 
      });
    });
  },
  
  // 获取巡检计划详情
  getPlanDetail(id) {
    return new Promise((resolve, reject) => {
      request({
        url: `/api/patrols/plans/${id}`,
        method: 'GET'
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取巡检计划详情失败，使用默认数据', err); 
      });
    });
  },
  
  // 更新巡检计划
  updatePlan(id, data) {
    return request({
      url: `/api/patrols/plans/${id}`,
      method: 'PUT',
      data
    });
  },
  
  // 删除巡检计划
  deletePlan(id) {
    return request({
      url: `/api/patrols/plans/${id}`,
      method: 'DELETE'
    });
  },
  
  // 获取巡检项目列表
  getItemList(params) {
    return new Promise((resolve, reject) => {
      // 检查是否有planId参数，有则使用按计划获取项目的接口
      if (params && params.planId) {
        request({
          url: `/api/patrols/plans/${params.planId}/items`,
          method: 'GET'
        })
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          console.warn('获取巡检计划项目列表失败，使用默认数据', err); 
        });
      } else { 
      }  
    });
  },
  
  // 获取巡检计划关联的执行人员列表
  getPlanExecutorList(params) {
    return new Promise((resolve, reject) => {
      if (!params || !params.planId) {
        resolve({
          code: 400,
          message: '缺少planId参数',
          data: []
        });
        return;
      }
      
      request({
        url: `/api/patrols/plans/${params.planId}/executors`,
        method: 'GET'
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取计划执行人员列表失败，使用默认数据', err);
        // 提供默认数据，避免页面错误
        resolve({
          code: 200,
          message: "获取计划执行人员列表成功(本地数据)",
          data: [
            {
              id: 1,
              name: "张工",
              position: "运维工程师",
              phone: "13800138001",
              skills: ["设备检查", "参数测量"]
            },
            {
              id: 2,
              name: "李工",
              position: "巡检员",
              phone: "13800138002",
              skills: ["设备检查", "管道巡视"]
            },
            {
              id: 3,
              name: "王工",
              position: "技术主管",
              phone: "13800138003",
              skills: ["设备检查", "故障诊断", "数据分析"]
            }
          ]
        });
      });
    });
  },

  // 获取巡检项目字典列表
  getDictionaryItemList(params) { 
    return new Promise((resolve, reject) => {
      request({
        url: '/api/patrols/dictionary/items',
        method: 'GET',
        data: params
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取巡检项目列表失败，使用默认数据', err);
        // 提供默认数据，避免页面错误
        resolve({
          code: 200,
          message: "获取巡检项目列表成功(本地数据)",
          data: [
            {
              id: 1,
              name: "泵压力检查",
              type: "parameter",
              standard: "0.6-0.8MPa"
            },
            {
              id: 2,
              name: "管道泄漏检查",
              type: "visual",
              standard: "无泄漏"
            },
            {
              id: 3,
              name: "阀门状态检查",
              type: "operation",
              standard: "阀门开关灵活"
            }
          ]
        });
      });
    });
  },
  
  // 创建巡检项目
  createItem(data) {
    return request({
      url: '/api/patrols/items',
      method: 'POST',
      data
    });
  },
  
  // 开始执行巡检计划
  startPlan(id) {
    return request({
      url: `/api/patrols/plans/${id}/start`,
      method: 'POST'
    });
  },
  
  // 完成巡检计划
  completePlan(id, data) {
    return request({
      url: `/api/patrols/plans/${id}/complete`,
      method: 'POST',
      data
    });
  },
  
  // 提交巡检结果
  submitPatrolResult(patrolRecordId, data) {
    return request({
      url: `/api/patrols/records/${patrolRecordId}/results`,
      method: 'POST',
      data
    });
  },
  
  // 获取巡检记录列表
  getPatrolRecords(params) {
    // 构建查询参数，按接口文档要求调整
    const queryParams = {
      status: params?.status || undefined,
      startDate: params?.startDate || undefined,
      endDate: params?.endDate || undefined,
      page: params?.page || 1,
      pageSize: params?.pageSize || 3
    };
	
	queryParams.executorId=uni.getStorageSync("userId");
	queryParams.role=uni.getStorageSync("userRole");
    
    // 移除undefined的属性
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === undefined) {
        delete queryParams[key];
      }
    });
    
    return new Promise((resolve, reject) => {
      request({
        url: '/api/patrols/records/list',
        method: 'POST', // 根据接口文档，使用POST方法
        data: queryParams
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取巡检记录列表失败，使用默认数据', err); 
      });
    });
  },
   
  // 获取巡检结果详情
  getPatrolResultDetail(recordId) {
    return request({
      url: `/api/patrols/records/${recordId}/results`,
      method: 'GET'
    });
  },
  
  // 获取巡检周期类型选项
  getScheduleTypes() {
    return [
      { label: '每日', value: 'daily' },
      { label: '每周', value: 'weekly' },
      { label: '每月', value: 'monthly' },
      { label: '自定义', value: 'custom' }
    ];
  },
  
  // 获取巡检检查类型选项
  getCheckTypes() {
    return [
      { label: '目视检查', value: 'visual' },
      { label: '参数检查', value: 'parameter' },
      { label: '操作检查', value: 'operation' }
    ];
  },
  
  // 获取巡检计划执行人列表
  getPlanExecutors(planId) {
    return new Promise((resolve, reject) => {
      request({
        url: `/api/patrols/plans/${planId}/executors`,
        method: 'GET'
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取巡检计划执行人列表失败，使用默认数据', err);
        // 提供默认数据，避免页面错误
        resolve({
          code: 200,
          message: "获取计划执行人员列表成功(本地数据)",
          data: [
             
          ]
        });
      });
    });
  },
  
  // 提交巡检记录
  submitPatrolRecord(data) {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/patrols/records',
        method: 'POST',
        data
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('提交巡检记录失败', err);
        // 提供默认响应，模拟提交成功
        resolve({
          code: 200,
          message: "巡检记录提交成功"
        });
      });
    });
  },
  
  // 获取巡检计划列表
  getPatrolPlans(params) {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/patrols/plans/list',
        method: 'POST',
        data: params
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取巡检计划列表失败，使用默认数据', err);
        // 提供默认数据，避免页面错误
        resolve({
          code: 200,
          message: "巡检列表查询成功",
          data: []
        });
      });
    });
  },
 
  // 获取巡检计划消息
  getPatrolPlansMessages(userId, role){
    return request({
      url: `/api/patrols/plans/messages`,
      method: 'GET',
      data: {
        uid: userId,
        role: role
      }
    });
  },
  
  // 获取有限数量的巡检工单
  getLimitedPatrolRecords(limit = 5) {
    const userId = uni.getStorageSync("userId");
    const role = uni.getStorageSync("userRole");
    
    return request({
      url: '/api/patrols/records/limit',
      method: 'GET',
      data: {
        uid: userId,
        role: role,
        limit: limit
      }
    });
  } 
};

// 设备模块API
const deviceApi = {
  getList(params) {
    // 参照接口文档处理参数
    const queryParams = {
      page: params?.page || 1,
      pageSize: params?.pageSize || 10,
      status: params?.status || '',
      area: params?.area || '',
      type: params?.type || '',
      keyword: params?.keyword || '',
      sortBy: params?.sortBy || '',
      sortOrder: params?.sortOrder || 'desc'
    };
    
    return request({
      url: '/api/devices/list',
      method: 'POST',
      data: queryParams
    });
  },
  getDetail(id) {
    return request({
      url: `/api/devices/${id}`,
      method: 'GET'
    });
  },
  getStats() {
    return request({
      url: '/api/device/stats',
      method: 'GET'
    });
  },
  // 根据热用户ID获取设备列表
  getDevicesByHeatUnitId(heatUnitId) {
    return new Promise((resolve, reject) => {
      request({
        url: `/api/devices/heat-unit/${heatUnitId}`,
        method: 'GET'
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn(`获取热用户(ID:${heatUnitId})的设备列表失败，使用默认数据`, err); 
        // 添加默认数据作为备用，防止UI卡住
        resolve({
          code: 200,
          message: "获取设备列表成功(本地默认数据)",
          data: [
            { id: '1001', name: '水泵1#', type: 'pump', deviceParent: 'heatstation' },
            { id: '1002', name: '水泵2#', type: 'pump', deviceParent: 'heatstation' },
            { id: '1003', name: '调节阀1#', type: 'valve', deviceParent: 'heatstation' },
            { id: '1004', name: '调节阀2#', type: 'valve', deviceParent: 'heatstation' },
            { id: '1005', name: '换热器', type: 'heatexchanger', deviceParent: 'heatstation' }
          ]
        });
      });
    });
  },
  
  // 根据设备ID获取设备巡检项
  getPatrolItemsByDeviceId(deviceId) {
    return new Promise((resolve, reject) => {
      request({
        url: `/api/devices/patrol-items/${deviceId}`,
        method: 'GET'
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn(`获取设备(ID:${deviceId})的巡检项失败，使用默认数据`, err); 
      });
    });
  }  
};

// 故障模块API
const faultApi = {
  reportFault(data) {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/faults/report',
        method: 'POST',
        data
      })
      .then(res => {
        if (res.code === 200) {
          resolve(res);
        } else {
          // 处理业务错误（状态码是200但业务状态不成功的情况）
          const errorMsg = res.message || '上报失败';
          reject(new Error(errorMsg));
        }
      })
      .catch(err => {
        // 处理网络错误或服务器错误
        console.error('故障上报请求失败:', err);
        
        // 尝试从错误响应中提取更有用的信息
        let errorMsg = '网络异常，请稍后重试';
        
        if (err.data && typeof err.data === 'object') {
          // 尝试从后端错误响应中提取错误信息
          if (err.data.message) {
            errorMsg = err.data.message;
          } else if (err.data.error) {
            errorMsg = err.data.error;
          }
        } else if (err.errMsg) {
          errorMsg = err.errMsg;
        }
        
        reject(new Error(errorMsg));
      });
    });
  },
  getFaultList(params) {
    // 构建符合后端接口规范的参数
   const queryParams = {
     status: params?.status,
     date: params?.date,
     page: params?.page || 1,
     pageSize: params?.pageSize || 10
   };
    
    // 添加热用户ID参数
    if (params && params.heatUnitId) {
      queryParams.heatUnitId = params.heatUnitId;
    }
    
    // 移除undefined的参数
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === undefined) {
        delete queryParams[key];
      }
    });
    
    return request({
      url: '/api/faults/list',
      method: 'GET',
      data: queryParams
    });
  },
  getFaultDetail(id) {
    return request({
      url: `/api/faults/detail/${id}`,
      method: 'GET'
    });
  }, 
  updateFaultStatus(requestData) {
   // 兼容两种调用方式
   return request({ // 使用封装的 request 函数
    url: '/api/faults/status', // API 路径
    method: 'POST',            // 请求方法
    data: {
      fault_id: requestData.fault_id,         // 故障ID
      operator_id: requestData.operator_id,   // 操作员ID
      fault_status: requestData.fault_status, // 目标状态 (例如 '已确认')
      repair_user_id: requestData.repair_user_id,
	  heat_unit_id: requestData.heat_unit_id // 添加热用户ID
    }
  })
}, 
  
  // 获取故障消息 /api/faults/messages
  getFaultMessages(userId, userRole) {
    return request({
      url: '/api/faults/messages',
      method: 'GET',
      data: {
        uid: userId,
        role: userRole,
        heatUnitId: uni.getStorageSync("heatUnitId")
      }
    });
  }  
};

// 室温API模块
const temperatureApi = {
  // 获取户外温度
  getOutdoorTemperature() {
    return request({
      url: '/api/temperature/outdoor',
      method: 'GET'
    });
  },
  
  // 获取室温列表
  getTemperatureList(params) {
    return request({
      url: '/api/temperatures/list',
      method: 'GET',
      data: params
    });
  },
  
  // 上报温度
  reportTemperature(data) {
    return request({
      url: '/api/temperature/report',
      method: 'POST',
      data: {
        heatUnitId: data.heatUnitId,
        buildingNo: data.buildingNo,
        unitNo: data.unitNo,
        roomNo: data.roomNo,
        indoorTemp: data.indoorTemp,
        outdoorTemp: data.outdoorTemp,
        longitude: data.longitude,
        latitude: data.latitude,
        remark: data.remark,
        images: data.images,
        reporterId: uni.getStorageSync('userId')
      }
    });
  },
  
  // 获取室温详情
  getTemperatureDetail(id) {
    return request({
      url: `/api/temperatures/${id}`,
      method: 'GET'
    });
  }
};

// 首页模块API
const homeApi = {
  // 获取首页概览数据
  getOverview() {
    return request({
      url: '/api/home/<USER>',
      method: 'GET'
    });
  },
  // 获取最近工单
  getRecentOrders(limit = 5) {
	const apiParams = {};
    apiParams.limit=limit;
	apiParams.uid=uni.getStorageSync("userId");
	apiParams.role=uni.getStorageSync("userRole");
	apiParams.heatUnitId=uni.getStorageSync("heatUnitId");
    return request({
      url: '/api/WorkOrders/list',
      method: 'GET',
      data: apiParams
    });
  },
  // 获取室外温度
  getOutdoorTemperature() {
    return temperatureApi.getOutdoorTemperature();
  },
  
  // 提交室温报告
  reportTemperature(data) {
    return temperatureApi.reportTemperature(data);
  },

  // 获取热用户总数
  getHeatUnitCount() {
    return request({
      url: '/api/heatunits/count',
      method: 'GET'
    });
  },

  // 获取换热站在线率
  getHeatUnitOnlineRate() {
    return request({
      url: '/api/hes/online-rate',
      method: 'GET'
    });
  },

  // 获取本周故障告警数量
  getWeeklyFaultCount() {
    return request({
      url: '/api/faults/weekly-count',
      method: 'GET'
    });
  } 
};

// 热用户API模块
const heatUnitApi = {
  // 获取热用户列表
  getList() {
    return request({
      url: '/api/heatunits/list',
      method: 'GET'
    });
  },
  
  // 获取用户关联的热力单位
  getByUserId(userId) {
    return new Promise((resolve, reject) => {
      // 获取用户的热力单位ID
      const heatUnitId = uni.getStorageSync('heatUnitId');
      console.log("获取到的原始heatUnitId:", heatUnitId);
      
      // 获取所有热力单位
      this.getList()
        .then(res => {
          if (res.code === 200 && res.data) {
            // 如果用户有关联的热力单位ID且不是0
            if (heatUnitId && heatUnitId !== '0' && heatUnitId !== 0) {
              // 处理可能包含多个项目ID的情况（逗号分隔）
              const userHeatUnitIds = heatUnitId.split(',').map(id => id.trim());
              
              // 过滤出用户关联的所有热力单位
              // 注意：将ID统一转为字符串进行比较，避免类型不匹配问题
              const userHeatUnits = res.data.filter(unit => 
                userHeatUnitIds.includes(String(unit.id)) || userHeatUnitIds.includes(Number(unit.id))
              );
              
              if (userHeatUnits && userHeatUnits.length > 0) {
                // 返回包含用户关联的热力单位的数组
                resolve({
                  code: 200,
                  message: "获取用户热力单位成功",
                  data: userHeatUnits
                });
              } else {
                // 如果未找到匹配的热力单位，返回所有热力单位
                console.log("未找到匹配的热力单位，返回所有热力单位");
                resolve({
                  code: 200,
                  message: "未找到匹配的热力单位，返回所有热力单位",
                  data: res.data
                });
              }
            } else {
              // 如果用户没有关联的热力单位ID或ID为0，则返回所有热力单位
              console.log("无热力单位ID或ID为0，返回所有热力单位");
              resolve({
                code: 200,
                message: "获取所有热力单位成功",
                data: res.data
              });
            }
          } else {
            // 如果获取热力单位列表失败，返回原始响应
            resolve(res);
          }
        })
        .catch(err => {
          reject(err);
        });
    });
  }
};

// 室温上报
export const temperatureReportApi = {
  submit(data) {
    return request({
      url: '/api/temperatures/report',
      method: 'POST',
      data
    });
  }
};

// 换热站API模块
const heatingStationApi = {
  // 获取换热站列表
  getList(params) {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/hes/list',
        method: 'POST',
        data: params
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取换热站列表失败，使用默认数据', err);  
      });
    });
  },
  
  // 获取换热站详情
  getDetail(id) {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/hes/detail',
        method: 'POST',
        data: { id }
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取换热站详情失败，使用默认数据', err);
        // 提供默认数据，避免页面错误
        resolve({
          code: 200,
          message: "换热站数据获取成功",
          data: {
            basic_info: {
              id: id,
              name: "金色家园换热站",
              hes_code: "HES001",
              heat_unit_name: "金色家园",
              status: "online",
              run_mode: "自动",
              calc_mode: "二次供水温度控制",
              used_year: 5,
              heating_area: 25000.5,
              design_load: 1500.0,
              design_flow: 30.0
            },
            realtime_data: {
              primary_system: {
                supply_temp: 85.5,
                return_temp: 60.2,
                supply_pressure: 0.6,
                return_pressure: 0.3,
                flow_rate: 25.6,
                power: 1200.5
              },
              secondary_system: {
                supply_temp: 65.3,
                return_temp: 45.1,
                supply_pressure: 0.4,
                return_pressure: 0.2,
                flow_rate: 28.3
              },
              equipment_status: {
                pumps: [
                  {
                    id: 1,
                    name: "一次泵1#",
                    status: "running",
                    frequency: 42.5,
                    current: 18.6,
                    power: 7.5
                  },
                  {
                    id: 2,
                    name: "一次泵2#",
                    status: "standby",
                    frequency: 0,
                    current: 0,
                    power: 7.5
                  }
                ],
                valves: [
                  {
                    id: 1,
                    name: "调节阀1#",
                    opening: 65,
                    status: "normal"
                  },
                  {
                    id: 2,
                    name: "调节阀2#",
                    opening: 40,
                    status: "normal"
                  }
                ]
              },
              last_update_time: "2025-03-01 12:30:45"
            }
          }
        });
      });
    });
  },
  
  // 换热站控制
  controlDevice(params) {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/hes/control',
        method: 'POST',
        data: params
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('换热站控制命令发送失败', err);
        // 实际生产环境不应提供默认数据，这里是为了开发和测试
        if (err.statusCode === 600) {
          reject({
            code: 600,
            message: "设备离线",
            details: "无法发送控制命令，请检查设备连接状态"
          });
        } else {
          reject(err);
        }
      });
    });
  },
  
  // 获取换热站远程协助信息
  getRemoteAssistance(hesId) {
    return request({
      url: '/api/hes/remote',
      method: 'POST',
      data: { hes_id: hesId }
    });
  },
  
  // 获取换热站历史数据
  getHistoryData(params) {
    return request({
      url: '/api/hes/history',
      method: 'POST',
      data: params
    });
  },
  
  // 获取换热站数据曲线
  getChartData(params) {
    return request({
      url: '/api/hes/chart',
      method: 'POST',
      data: params
    });
  },
  
  // 获取换热站告警列表
  getAlarmList(params) {
    return new Promise((resolve, reject) => {
      request({
        url: '/api/hes/alarms/list',
        method: 'POST',
        data: params
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        console.warn('获取换热站告警列表失败', err);
        // 提供默认数据，避免页面错误
        resolve({
          code: 200,
          message: "告警列表获取成功",
          data: {
            total: 2,
            list: [
              {
                id: 1,
                hes_id: params.hes_id || 1,
                hes_name: "金色家园换热站",
                alarm_type: "热源",
                alarm_name: "一次供水温度过高",
                level: "urgent",
                value: 95.5,
                threshold: 90.0,
                status: "active",
                start_time: "2025-03-01 10:15:30",
                end_time: null,
                duration: "02:45:12"
              },
              {
                id: 2,
                hes_id: params.hes_id || 1,
                hes_name: "金色家园换热站",
                alarm_type: "压力",
                alarm_name: "二次回水压力过低",
                level: "warning",
                value: 0.15,
                threshold: 0.2,
                status: "active",
                start_time: "2025-03-01 11:05:22",
                end_time: null,
                duration: "01:55:20"
              }
            ]
          }
        });
      });
    });
  },
  
  // 获取换热站告警详情
  getAlarmDetail(id) {
    return request({
      url: '/api/hes/alarms/detail',
      method: 'POST',
      data: { id }
    });
  },
  
  // 获取换热站告警统计
  getAlarmStats(params) {
    return request({
      url: '/api/hes/alarms/stats',
      method: 'POST',
      data: params
    });
  },
  
  // 获取换热站在线率
  getOnlineRate() {
    return request({
      url: '/api/hes/online-rate',
      method: 'GET'
    });
  }
};

// 考勤管理API模块
const attendanceApi = {
  // 提交打卡记录
  submitClock(data) {
    return request({
      url: '/api/attendance/clock',
      method: 'POST',
      data
    });
  },
  
  // 获取考勤记录
  getRecords(params = {}) {
    const { userId, year, month, day, status, startDate, endDate, keyword } = params;
    let url = '/api/attendance/records';
    
    // 构建查询参数
    const queryParams = [];
    if (userId) queryParams.push(`user_id=${userId}`);
    if (year) queryParams.push(`year=${year}`);
    if (month) queryParams.push(`month=${month}`);
    if (day) queryParams.push(`day=${day}`);
    if (status) queryParams.push(`status=${status}`);
    if (startDate) queryParams.push(`startDate=${startDate}`);
    if (endDate) queryParams.push(`endDate=${endDate}`);
    if (keyword) queryParams.push(`keyword=${encodeURIComponent(keyword)}`);
    
    if (queryParams.length > 0) {
      url += `?${queryParams.join('&')}`;
    }
    
    return request({
      url,
      method: 'GET'
    });
  },
  
  // 获取考勤统计
  getStats(params = {}) {
    const { userId, year, month, day } = params;
    let url = '/api/attendance/stats';
    
    // 构建查询参数
    const queryParams = [];
    if (userId) queryParams.push(`user_id=${userId}`);
    if (year) queryParams.push(`year=${year}`);
    if (month) queryParams.push(`month=${month}`);
    if (day) queryParams.push(`day=${day}`);
    
    if (queryParams.length > 0) {
      url += `?${queryParams.join('&')}`;
    }
    
    return request({
      url,
      method: 'GET'
    });
  },
  
  // 获取今日打卡记录
  getTodayRecord() 
  {
	  const apiParams = {
	  };
	  apiParams.user_id = uni.getStorageSync("userId");
      return request({
      url: '/api/attendance/today',
      method: 'GET',
	  data:apiParams
    });
  },
  
  // 获取最近N天的打卡记录
  getRecentRecords(days = 7) { 
	  const apiParams = {
		  days  
	  };
	    apiParams.user_id = uni.getStorageSync("userId");
    return request({
      url: '/api/attendance/recent',
      method: 'GET',
      data: apiParams
    });
  },
  
  // 获取打卡规则
  getClockRules() {
    return request({
      url: '/api/attendance/rules',
      method: 'GET'
    });
  },
  
  // 更新打卡规则
  updateClockRules(clockInTime, clockOutTime) {
    return request({
      url: '/api/attendance/updaterules',
      method: 'POST',
      data: {
        clock_in_time: clockInTime,
        clock_out_time: clockOutTime
      }
    });
  },
  
  // 检查是否在考勤范围内
  checkAttendanceArea(data) {
    return request({
      url: '/api/attendance/check-area',
      method: 'POST',
      data
    });
  },
  
  // 提交补卡申请
  submitSupplementApplication(data) {
    return request({
      url: '/api/attendance/supplement',
      method: 'POST',
      data
    });
  },
  
  // 获取员工考勤列表（管理员用）
  getStaffAttendance(params = {}) {
    return request({
      url: '/api/attendance/staff',
      method: 'GET',
      data: params
    });
  },
  
  // 获取部门考勤统计（管理员用）
  getDepartmentStats(params = {}) {
    return request({
      url: '/api/attendance/department-stats',
      method: 'GET',
      data: params
    });
  },
  
  // 审批补卡申请（管理员用）
  approveSupplementApplication(data) {
    return request({
      url: '/api/attendance/supplement-approve',
      method: 'POST',
      data
    });
  },
  
  // 获取所有员工列表（用于统计页面的员工选择）
  getAllStaff() {
    return request({
      url: '/api/attendance/all-staff',
      method: 'GET'
    });
  },
  
  // 导出考勤数据
  exportAttendance(params = {}) {
    return request({
      url: '/api/attendance/export',
      method: 'POST',
      data: params
    });
  },

  /**
   * 上传员工位置轨迹
   * @param {Object} data 包含userId, longitude, latitude的对象
   * @returns {Promise} 上传结果
   */
  uploadPersonTrajectory(data) {
    console.log('准备上传位置轨迹:', data);
    
    // 检查参数有效性
    if (!data.userId || !data.longitude || !data.latitude) {
      console.error('上传位置轨迹参数无效:', data);
      return Promise.reject({errMsg: '位置参数无效'});
    }
    
    // 构建查询参数字符串，同时传递userId和employeeId（使用相同的值）
    const url = `/api/person/trajectory/location?userId=${data.userId}&employeeId=${data.userId}&longitude=${data.longitude}&latitude=${data.latitude}`;
    
    // 特殊处理：直接使用uni.request发起请求，处理201状态码
    return new Promise((resolve, reject) => {
      // 获取token
      const token = uni.getStorageSync('token');
      const header = {
        'Content-Type': 'application/json'
      };
      
      // 添加token (如果有)
      if (token) {
        header.Authorization = `Bearer ${token}`;
      }
      
      uni.request({
        url: BASE_URL + url,
        method: 'POST',
        header: header,
        success: (res) => {
          console.log('位置轨迹上传响应:', res);
          
          // 特别处理201状态码，将其视为成功
          if ((res.statusCode === 200 || res.statusCode === 201) && 
              res.data && res.data.code === 200) {
            resolve(res.data);
          } else {
            reject({
              errMsg: `请求失败: ${res.statusCode}`,
              statusCode: res.statusCode,
              data: res.data
            });
          }
        },
        fail: (err) => {
          console.error('位置轨迹上传请求失败:', err);
          reject(err);
        }
      });
    });
  }
};

//  告警管理  
const alarmApi = {
  // 获取告警列表
  getAlarmList(userId, role) {
    console.log(`调用获取告警列表: userId=${userId}, role=${role}`);
    return request({ // 使用封装的 request 函数
      url: '/api/alarm/messages', // 后端接口路径
      method: 'GET',
      data: {
        uid: userId,
        role: role,
        heatUnitId: uni.getStorageSync("heatUnitId")
      }
    });
  }, 
  
  // 更新告警状态
  updateAlarmStatus(alarmId, status) {
    console.log(`调用更新告警状态API: alarmId=${alarmId}, status=${status}`);
    return request({ // 使用封装的 request 函数
      url: '/api/alarm/updateStatus', // 后端接口路径
      method: 'POST', // 根据接口文档和实践，推测使用 POST
      data: {
        alarm_id: alarmId,    // 使用下划线命名，匹配接口文档中的 request body
        alarm_status: status // 接口文档中这个键名包含空格，需要加引号
      }
    });
  }
};
 
/**
 * 系统字典API
 */
const dictApi = {
  /**
   * 根据字典ID获取字典数据列表
   * @param {Number} dictId 字典ID
   * @returns {Promise} 包含字典数据的Promise对象
   */
  getDictDataByDictId(dictId) {
    return request({
      url: `/api/dict/data/${dictId}`,
      method: 'GET'
    });
  }
};
// 导出API模块
export {
  BASE_URL,
  request,
  testApiConnection,
  userApi,
  workOrderApi,
  patrolApi,
  deviceApi,
  faultApi,
  homeApi,
  temperatureApi,
  heatUnitApi,
  heatingStationApi,
  attendanceApi,
  alarmApi,
  materialsApi,
  dictApi
};

/**
 * 系统API
 */
export const systemApi = {
  /**
   * 获取系统参数
   * @returns {Promise} 包含系统参数的Promise对象
   */
  getSystemParams() {
    return request({
      url: '/api/system/params',
      method: 'GET'
    });
  }
};

 