2025-08-25T09:49:13.329+08:00  INFO 16628 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 16628 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-25T09:49:13.367+08:00 DEBUG 16628 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-25T09:49:13.368+08:00  INFO 16628 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-25T09:49:18.598+08:00  INFO 16628 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-25T09:49:18.714+08:00  INFO 16628 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 109 ms. Found 34 JPA repository interfaces.
2025-08-25T09:49:19.201+08:00  INFO 16628 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-25T09:49:19.210+08:00  INFO 16628 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-25T09:49:19.210+08:00  INFO 16628 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-25T09:49:19.255+08:00  INFO 16628 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-25T09:49:19.256+08:00  INFO 16628 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5840 ms
2025-08-25T09:49:19.313+08:00 DEBUG 16628 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-25T09:49:19.449+08:00  INFO 16628 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-25T09:49:19.501+08:00  INFO 16628 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-25T09:49:19.529+08:00  INFO 16628 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-25T09:49:19.631+08:00  INFO 16628 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-25T09:49:23.678+08:00  INFO 16628 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@42b43a6
2025-08-25T09:49:23.679+08:00  INFO 16628 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-25T09:49:23.950+08:00  INFO 16628 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-25T09:49:25.257+08:00  INFO 16628 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-25T09:49:25.862+08:00  INFO 16628 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-25T09:49:26.062+08:00  INFO 16628 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-25T09:49:27.737+08:00  INFO 16628 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-25T09:49:27.838+08:00  INFO 16628 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-08-25T09:49:27.876+08:00  INFO 16628 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@67adae9f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@16f9bba1, org.springframework.security.web.context.SecurityContextHolderFilter@5246f452, org.springframework.security.web.header.HeaderWriterFilter@22620b28, org.springframework.web.filter.CorsFilter@57c3e84, org.springframework.security.web.authentication.logout.LogoutFilter@3e5d7d37, com.heating.filter.JwtAuthenticationFilter@7aa01bd9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2217de6c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@352c7b39, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1421a9cc, org.springframework.security.web.access.ExceptionTranslationFilter@4e9f3885, org.springframework.security.web.access.intercept.AuthorizationFilter@346df1c0]
2025-08-25T09:49:28.152+08:00  INFO 16628 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-25T09:49:28.161+08:00  INFO 16628 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 15.294 seconds (process running for 16.319)
2025-08-25T09:49:35.705+08:00  INFO 16628 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25T09:49:35.705+08:00  INFO 16628 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-25T09:49:35.706+08:00  INFO 16628 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-25T09:49:35.723+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-25T09:49:35.739+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-25T09:49:35.753+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-25T09:49:35.754+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-25T09:49:35.762+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-25T09:49:35.763+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-25T09:49:35.763+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-25T09:49:35.763+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-25T09:49:45.708+08:00 DEBUG 16628 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-25T09:49:45.709+08:00 DEBUG 16628 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-25T09:49:46.277+08:00 DEBUG 16628 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-25T09:49:46.282+08:00 DEBUG 16628 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-25T09:49:46.283+08:00 DEBUG 16628 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:46.283+08:00 DEBUG 16628 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:46.303+08:00 DEBUG 16628 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-25T09:49:47.725+08:00 DEBUG 16628 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-25T09:49:47.726+08:00 DEBUG 16628 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.726+08:00 DEBUG 16628 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.730+08:00 DEBUG 16628 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-25T09:49:47.729+08:00 DEBUG 16628 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-25T09:49:47.731+08:00 DEBUG 16628 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.731+08:00 DEBUG 16628 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.731+08:00 DEBUG 16628 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-25T09:49:47.731+08:00 DEBUG 16628 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.731+08:00 DEBUG 16628 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.731+08:00 DEBUG 16628 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.731+08:00 DEBUG 16628 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.732+08:00 DEBUG 16628 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-25T09:49:47.732+08:00 DEBUG 16628 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.732+08:00 DEBUG 16628 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.733+08:00 DEBUG 16628 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-25T09:49:47.734+08:00 DEBUG 16628 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-25T09:49:47.735+08:00 DEBUG 16628 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-25T09:49:47.737+08:00 DEBUG 16628 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-25T09:49:47.739+08:00  INFO 16628 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-25T09:49:47.739+08:00  INFO 16628 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-25T09:49:47.740+08:00 DEBUG 16628 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-25T09:49:47.741+08:00  INFO 16628 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-25T09:49:47.741+08:00  INFO 16628 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-25T09:49:47.742+08:00  INFO 16628 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-25T09:49:47.742+08:00  INFO 16628 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-25T09:49:47.742+08:00 DEBUG 16628 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-25T09:49:47.820+08:00  INFO 16628 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到0条巡检工单记录
2025-08-25T09:49:47.824+08:00 DEBUG 16628 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-25T09:49:47.824+08:00 DEBUG 16628 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-25T09:49:47.825+08:00 DEBUG 16628 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.825+08:00 DEBUG 16628 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.825+08:00 DEBUG 16628 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.825+08:00 DEBUG 16628 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.828+08:00 DEBUG 16628 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-25T09:49:47.828+08:00 DEBUG 16628 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-25T09:49:47.829+08:00  INFO 16628 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-25T09:49:47.829+08:00  INFO 16628 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-25T09:49:47.829+08:00  INFO 16628 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-25T09:49:47.865+08:00  INFO 16628 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取到0条巡检工单记录
2025-08-25T09:49:47.867+08:00  INFO 16628 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=3, 总页数=1
2025-08-25T09:49:47.870+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-25T09:49:47.871+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.871+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.876+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-25T09:49:47.877+08:00  INFO 16628 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-25T09:49:47.877+08:00 DEBUG 16628 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-25T09:49:47.914+08:00  INFO 16628 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=3, 总页数=1
2025-08-25T09:49:47.988+08:00 DEBUG 16628 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-25T09:49:47.989+08:00 DEBUG 16628 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:47.989+08:00 DEBUG 16628 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:47.992+08:00 DEBUG 16628 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-25T09:49:48.037+08:00 DEBUG 16628 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-25T09:49:48.037+08:00 DEBUG 16628 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:48.037+08:00 DEBUG 16628 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:48.040+08:00 DEBUG 16628 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-25T09:49:48.040+08:00  INFO 16628 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-25T09:49:48.040+08:00  INFO 16628 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-25T09:49:48.739+08:00 DEBUG 16628 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-25T09:49:48.739+08:00 DEBUG 16628 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:48.739+08:00 DEBUG 16628 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:48.741+08:00 DEBUG 16628 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-25T09:49:48.742+08:00  INFO 16628 --- [http-nio-8889-exec-9] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
2025-08-25T09:49:49.021+08:00 DEBUG 16628 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/attendance/today?user_id=6
2025-08-25T09:49:49.021+08:00 DEBUG 16628 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-25T09:49:49.022+08:00 DEBUG 16628 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-25T09:49:49.024+08:00 DEBUG 16628 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/attendance/today?user_id=6
2025-08-25T09:49:49.025+08:00  INFO 16628 --- [http-nio-8889-exec-8] c.h.service.impl.AttendanceServiceImpl   : Getting today's attendance for user ID: 6
