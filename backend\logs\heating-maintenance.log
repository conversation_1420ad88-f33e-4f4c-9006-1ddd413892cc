2025-08-22T08:24:29.668+08:00  INFO 11032 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 11032 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T08:24:29.684+08:00 DEBUG 11032 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T08:24:29.685+08:00  INFO 11032 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T08:24:31.451+08:00  INFO 11032 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T08:24:31.565+08:00  INFO 11032 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 107 ms. Found 34 JPA repository interfaces.
2025-08-22T08:24:32.097+08:00  INFO 11032 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T08:24:32.107+08:00  INFO 11032 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T08:24:32.108+08:00  INFO 11032 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T08:24:32.157+08:00  INFO 11032 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T08:24:32.158+08:00  INFO 11032 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2438 ms
2025-08-22T08:24:32.216+08:00 DEBUG 11032 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T08:24:32.337+08:00  INFO 11032 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T08:24:32.407+08:00  INFO 11032 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T08:24:32.439+08:00  INFO 11032 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T08:24:32.540+08:00  INFO 11032 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T08:24:36.140+08:00  INFO 11032 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@389008d1
2025-08-22T08:24:36.156+08:00  INFO 11032 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T08:24:36.457+08:00  INFO 11032 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T08:24:37.608+08:00  INFO 11032 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T08:24:38.179+08:00  INFO 11032 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T08:24:38.391+08:00  INFO 11032 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T08:24:39.973+08:00  INFO 11032 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-22T08:24:40.057+08:00  INFO 11032 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-08-22T08:24:40.099+08:00  INFO 11032 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6df78021, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b47646a, org.springframework.security.web.context.SecurityContextHolderFilter@34ee9000, org.springframework.security.web.header.HeaderWriterFilter@6846d654, org.springframework.web.filter.CorsFilter@67b8f02e, org.springframework.security.web.authentication.logout.LogoutFilter@35d0499b, com.heating.filter.JwtAuthenticationFilter@2e6d76ba, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6546c39, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3bd3d820, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6fdc624, org.springframework.security.web.access.ExceptionTranslationFilter@214b9b5, org.springframework.security.web.access.intercept.AuthorizationFilter@59fd35ad]
2025-08-22T08:24:40.362+08:00  INFO 11032 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-22T08:24:40.369+08:00  INFO 11032 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 11.068 seconds (process running for 12.242)
2025-08-22T08:41:00.121+08:00  INFO 11032 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22T08:41:00.121+08:00  INFO 11032 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-22T08:41:00.122+08:00  INFO 11032 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-22T08:41:00.136+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-22T08:41:00.151+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T08:41:00.164+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-22T08:41:00.164+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T08:41:00.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-22T08:41:00.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T08:41:00.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-22T08:41:00.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T08:41:55.282+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-22T08:41:55.283+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-22T08:41:55.795+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T08:41:55.800+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-22T08:41:55.801+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:55.801+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:55.825+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-22T08:41:57.269+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T08:41:57.269+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.269+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.270+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.272+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T08:41:57.273+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.273+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.273+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T08:41:57.274+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.274+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.275+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T08:41:57.276+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T08:41:57.276+08:00  INFO 11032 --- [http-nio-8889-exec-10] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T08:41:57.276+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T08:41:57.277+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T08:41:57.277+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T08:41:57.277+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T08:41:57.284+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T08:41:57.284+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T08:41:57.285+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T08:41:57.285+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T08:41:57.285+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T08:41:57.358+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T08:41:57.359+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.359+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.362+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T08:41:57.413+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T08:41:57.415+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T08:41:57.415+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.415+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.417+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T08:41:57.418+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T08:41:57.418+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T08:41:57.453+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T08:41:57.512+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T08:41:57.512+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.512+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.515+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T08:41:57.545+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T08:41:57.546+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:57.546+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:57.557+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T08:41:57.558+08:00  INFO 11032 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T08:41:57.558+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T08:41:58.588+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T08:41:58.592+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T08:41:58.592+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T08:41:58.592+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T08:41:58.595+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T08:41:58.596+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T08:41:58.596+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T08:41:58.596+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T08:41:59.880+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:07:33.724+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-08-22T09:07:33.724+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:07:33.724+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:07:33.727+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-08-22T09:07:33.730+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取巡检记录列表请求: role=admin, executorId=6, status=null
2025-08-22T09:07:33.730+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=1, pageSize=5, role=admin)
2025-08-22T09:07:33.732+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 管理员角色，获取所有巡检记录
2025-08-22T09:07:37.476+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/patrols/records/list
2025-08-22T09:07:37.477+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:07:37.477+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:07:37.479+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/patrols/records/list
2025-08-22T09:07:37.480+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取巡检记录列表请求: role=admin, executorId=6, status=null
2025-08-22T09:07:37.480+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取巡检记录列表: PatrolRecordListRequest(status=null, executorId=6, startDate=null, endDate=null, page=2, pageSize=5, role=admin)
2025-08-22T09:07:37.480+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 管理员角色，获取所有巡检记录
2025-08-22T09:07:41.550+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:07:41.550+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:07:41.550+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:07:41.551+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:07:41.551+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:07:41.551+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:07:41.552+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:07:41.552+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:07:41.552+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:07:41.553+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:07:41.554+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:07:41.554+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:07:41.554+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:07:41.556+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:07:41.556+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:07:41.556+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:07:41.556+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:07:41.556+08:00  INFO 11032 --- [http-nio-8889-exec-7] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:07:41.556+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:07:41.557+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:07:41.558+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:07:41.558+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:07:41.559+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:07:41.559+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:07:41.559+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:07:41.559+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:07:41.560+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:07:41.623+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:07:42.986+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:32:59.896+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-22T09:32:59.897+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T09:32:59.898+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-22T09:32:59.898+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T09:32:59.898+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-22T09:32:59.898+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T09:32:59.899+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-22T09:32:59.899+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T09:33:14.068+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-22T09:33:14.068+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-22T09:33:14.381+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T09:33:14.385+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-22T09:33:14.385+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:14.385+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:14.388+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-22T09:33:15.762+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:15.763+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:15.763+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:15.763+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:33:15.764+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:15.764+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:15.765+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:33:15.765+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:15.766+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:15.766+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:15.766+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:33:15.766+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:33:15.767+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:33:15.767+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:33:15.768+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:33:15.768+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:15.768+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:15.768+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:15.768+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:15.769+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:33:15.770+08:00  INFO 11032 --- [http-nio-8889-exec-9] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:33:15.770+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:33:15.770+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:33:15.770+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:33:15.770+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:33:15.771+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:33:15.771+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:33:15.838+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:33:15.841+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:33:15.841+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:15.841+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:15.841+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:15.841+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:15.841+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:15.844+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:33:15.845+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:15.846+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:33:15.846+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:33:15.881+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:33:16.006+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:33:16.007+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:16.007+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:16.010+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:33:16.040+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:33:16.040+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:16.040+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:16.042+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:33:16.043+08:00  INFO 11032 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:33:16.043+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:33:16.672+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:16.672+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:16.672+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:16.674+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:16.676+08:00  INFO 11032 --- [http-nio-8889-exec-6] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T09:33:16.677+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T09:33:16.677+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T09:33:17.158+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:33:17.159+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:33:17.159+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:17.159+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:17.161+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:33:17.162+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:33:17.162+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:33:17.162+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:33:18.433+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:18.434+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:18.434+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:18.436+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:18.437+08:00  INFO 11032 --- [http-nio-8889-exec-3] com.heating.controller.FaultController   : 获取故障列表: status=待确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T09:33:18.438+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T09:33:18.438+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T09:33:18.512+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:33:18.979+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:18.980+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:18.980+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:18.983+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:18.984+08:00  INFO 11032 --- [http-nio-8889-exec-5] com.heating.controller.FaultController   : 获取故障列表: status=已确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T09:33:18.984+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T09:33:18.984+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T09:33:19.430+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%B7%B2%E9%80%80%E5%9B%9E&page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:19.430+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:19.430+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:19.433+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%B7%B2%E9%80%80%E5%9B%9E&page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:19.433+08:00  INFO 11032 --- [http-nio-8889-exec-1] com.heating.controller.FaultController   : 获取故障列表: status=已退回, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T09:33:19.433+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T09:33:19.433+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T09:33:19.799+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:19.799+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:19.799+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:19.802+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:19.803+08:00  INFO 11032 --- [http-nio-8889-exec-2] com.heating.controller.FaultController   : 获取故障列表: status=待确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T09:33:19.803+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T09:33:19.803+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T09:33:20.369+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:20.370+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:20.370+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:20.372+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T09:33:20.373+08:00  INFO 11032 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T09:33:20.373+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T09:33:20.373+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T09:33:21.122+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:21.122+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:21.122+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:21.123+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:33:21.123+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:21.123+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:21.124+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:21.124+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:33:21.124+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:21.124+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:21.125+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:33:21.125+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:33:21.125+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:33:21.126+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:33:21.126+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:21.126+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:21.126+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:33:21.128+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:33:21.128+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:33:21.128+08:00  INFO 11032 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:33:21.128+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:33:21.128+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:21.128+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:21.131+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:33:21.132+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:33:21.132+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:33:21.132+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:33:21.192+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:33:22.462+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:33:49.161+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:33:49.162+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:49.162+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:49.164+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:33:49.165+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:33:49.165+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:33:49.243+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:33:49.442+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:33:49.638+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:33:49.838+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:33:50.067+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:33:50.302+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:33:50.508+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:33:50.707+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:33:50.707+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:33:50.727+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:50.728+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:50.728+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:50.730+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:50.731+08:00  INFO 11032 --- [http-nio-8889-exec-5] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:33:50.731+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:33:50.816+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-08-22T09:33:50.821+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:50.821+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:50.821+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:50.823+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:50.824+08:00  INFO 11032 --- [http-nio-8889-exec-1] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:33:50.825+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:33:50.903+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:50.903+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:33:50.903+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:33:50.905+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:33:50.906+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:33:50.906+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-08-22T09:33:50.941+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到1条待接单工单
2025-08-22T09:34:05.178+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:34:05.178+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:34:05.178+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:34:05.179+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:34:05.179+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:34:05.179+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:34:05.180+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:34:05.181+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:34:05.181+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:34:05.181+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:34:05.181+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:34:05.181+08:00  INFO 11032 --- [http-nio-8889-exec-10] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:34:05.181+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:34:05.181+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:34:05.181+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:34:05.181+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:34:05.181+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:34:05.182+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:34:05.182+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:34:05.183+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:34:05.183+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:34:05.183+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:34:05.183+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:34:05.185+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:34:05.185+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:34:05.185+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:34:05.185+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:34:05.251+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:34:06.514+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:36:13.907+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:36:13.908+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:13.908+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:13.910+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:36:13.910+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:36:13.910+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:36:13.978+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:36:14.175+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:36:14.389+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:36:14.519+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:14.519+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:14.519+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:14.519+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:36:14.520+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:14.520+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:14.521+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:36:14.521+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:14.521+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:14.521+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:14.522+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:36:14.522+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:36:14.522+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:36:14.522+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:36:14.523+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:36:14.523+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:14.523+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:14.523+08:00  INFO 11032 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:36:14.523+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:36:14.523+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:36:14.523+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:14.523+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:14.524+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:36:14.525+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:36:14.525+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:36:14.525+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:36:14.525+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:36:14.599+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:36:14.600+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:36:14.818+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:36:15.022+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:36:15.223+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:36:15.432+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:36:15.432+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:36:15.437+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:15.438+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:15.438+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:15.439+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:15.440+08:00  INFO 11032 --- [http-nio-8889-exec-1] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:36:15.440+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:36:15.508+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-08-22T09:36:15.511+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:15.512+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:15.512+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:15.513+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:15.514+08:00  INFO 11032 --- [http-nio-8889-exec-5] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:36:15.514+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:36:15.587+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:15.588+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:15.588+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:15.590+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:15.590+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:36:15.590+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-08-22T09:36:15.623+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到1条待接单工单
2025-08-22T09:36:15.857+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:36:20.760+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:36:20.760+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:20.760+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:20.762+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:36:20.763+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:36:20.763+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:36:20.836+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:36:21.060+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:36:21.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:36:21.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:36:21.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:21.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:21.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:21.174+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:21.175+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:36:21.176+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:21.176+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:21.176+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:36:21.176+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:21.176+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:21.173+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:21.177+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:21.177+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:21.177+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:36:21.178+08:00  INFO 11032 --- [http-nio-8889-exec-10] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:36:21.178+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:36:21.178+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:36:21.179+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:36:21.179+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:36:21.179+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:36:21.179+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:36:21.182+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:36:21.185+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:21.185+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:36:21.185+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:36:41.129+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:36:41.130+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:41.130+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-12] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:36:41.134+08:00 DEBUG 11032 --- [http-nio-8889-exec-12] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:41.134+08:00 DEBUG 11032 --- [http-nio-8889-exec-12] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:41.134+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:41.133+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:41.134+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:36:44.922+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:44.922+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:36:44.923+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:44.925+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:36:44.926+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:36:44.926+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:36:44.926+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:36:44.927+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:36:44.928+08:00  INFO 11032 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:36:44.928+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:36:44.928+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:36:44.928+08:00 DEBUG 11032 --- [http-nio-8889-exec-12] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:36:44.930+08:00  INFO 11032 --- [http-nio-8889-exec-12] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:36:44.930+08:00  INFO 11032 --- [http-nio-8889-exec-12] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:36:44.930+08:00  INFO 11032 --- [http-nio-8889-exec-12] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:36:44.930+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:36:44.930+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:44.930+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:44.930+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:36:44.931+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:44.931+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:44.931+08:00 DEBUG 11032 --- [http-nio-8889-exec-14] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:36:44.932+08:00 DEBUG 11032 --- [http-nio-8889-exec-14] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:44.932+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:36:44.932+08:00 DEBUG 11032 --- [http-nio-8889-exec-14] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:44.932+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:36:44.932+08:00  INFO 11032 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:36:44.932+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:36:44.932+08:00 DEBUG 11032 --- [http-nio-8889-exec-13] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:36:44.933+08:00 DEBUG 11032 --- [http-nio-8889-exec-13] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:44.933+08:00 DEBUG 11032 --- [http-nio-8889-exec-13] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:44.933+08:00 DEBUG 11032 --- [http-nio-8889-exec-14] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:36:44.935+08:00 DEBUG 11032 --- [http-nio-8889-exec-13] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:36:44.935+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:36:44.935+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:36:44.936+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:36:44.936+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:44.936+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:45.346+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:36:45.347+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:36:45.347+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:36:45.347+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:36:45.701+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:45.702+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:36:45.703+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:45.703+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:45.706+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:46.372+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:36:46.372+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:36:51.606+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:51.606+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:51.606+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:55.461+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:55.462+08:00  INFO 11032 --- [http-nio-8889-exec-11] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:36:55.462+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:36:56.619+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:36:57.177+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:36:57.178+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-08-22T09:36:58.236+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:58.236+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:58.236+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:58.241+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:58.242+08:00  INFO 11032 --- [http-nio-8889-exec-8] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:36:58.242+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:36:58.592+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:36:58.592+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:36:59.469+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:59.469+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:36:59.469+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:36:59.474+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:36:59.474+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:36:59.475+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-08-22T09:37:00.348+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:37:00.414+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到1条待接单工单
2025-08-22T09:37:00.502+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:37:00.523+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:37:00.523+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:37:00.615+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:37:00.748+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:37:00.748+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:37:00.816+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:37:01.029+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:37:01.029+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:37:01.030+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:37:01.237+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:37:01.272+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:37:01.304+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:37:01.304+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:37:01.307+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:37:01.356+08:00  INFO 11032 --- [http-nio-8889-exec-12] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:37:01.439+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:37:01.535+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:37:01.609+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:37:01.636+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:37:01.636+08:00  INFO 11032 --- [http-nio-8889-exec-1] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:37:01.943+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:37:02.482+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:37:02.483+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:37:15.264+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:37:15.264+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:15.265+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:15.266+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:37:15.267+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:37:15.267+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:37:15.349+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:37:15.571+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:37:15.768+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:37:15.817+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:15.817+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:37:15.817+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:15.817+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:15.817+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:15.817+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:15.818+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:37:15.818+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:37:15.819+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:15.819+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:15.819+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:15.819+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:15.820+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:37:15.820+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:15.820+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:15.820+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:37:15.820+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:15.821+08:00 DEBUG 11032 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:37:15.821+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:37:15.821+08:00  INFO 11032 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:37:15.821+08:00  INFO 11032 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:37:15.821+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:37:15.821+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:37:15.821+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:37:15.822+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:37:15.822+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:37:15.822+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:37:44.530+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:37:44.531+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:44.531+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:44.531+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:37:44.532+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:44.532+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:44.533+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:37:44.533+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:37:44.533+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:37:44.534+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:37:44.535+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:44.535+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:44.535+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:44.535+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:44.535+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.244+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:37:45.245+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:37:45.245+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:37:45.245+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:37:45.247+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:37:45.247+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.247+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.247+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:37:45.247+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.247+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.248+08:00 DEBUG 11032 --- [http-nio-8889-exec-14] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:37:45.249+08:00 DEBUG 11032 --- [http-nio-8889-exec-14] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.249+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:37:45.249+08:00 DEBUG 11032 --- [http-nio-8889-exec-14] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.249+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:37:45.249+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:37:45.249+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:37:45.250+08:00 DEBUG 11032 --- [http-nio-8889-exec-14] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:37:45.250+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:44.535+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.251+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.251+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:37:45.251+08:00 DEBUG 11032 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:37:45.249+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:37:45.251+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:37:45.251+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:37:45.251+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:45.251+08:00 DEBUG 11032 --- [http-nio-8889-exec-12] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:37:45.251+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.251+08:00 DEBUG 11032 --- [http-nio-8889-exec-12] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.251+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.251+08:00 DEBUG 11032 --- [http-nio-8889-exec-12] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.251+08:00  INFO 11032 --- [http-nio-8889-exec-14] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:37:45.252+08:00  INFO 11032 --- [http-nio-8889-exec-14] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:37:45.532+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:37:45.532+08:00 DEBUG 11032 --- [http-nio-8889-exec-12] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:37:45.533+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.533+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.534+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:37:45.534+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:45.534+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.534+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.534+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:45.534+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:45.536+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:45.536+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:37:45.536+08:00  INFO 11032 --- [http-nio-8889-exec-16] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:37:45.536+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:37:45.537+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:37:45.537+08:00 DEBUG 11032 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:37:46.105+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:46.106+08:00  INFO 11032 --- [http-nio-8889-exec-10] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:37:46.106+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:37:46.112+08:00 DEBUG 11032 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:37:46.113+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:37:46.114+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:37:46.115+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:37:46.115+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:37:46.370+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:37:46.371+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-08-22T09:37:46.379+08:00 DEBUG 11032 --- [http-nio-8889-exec-13] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:46.379+08:00 DEBUG 11032 --- [http-nio-8889-exec-13] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:37:46.380+08:00 DEBUG 11032 --- [http-nio-8889-exec-13] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:37:46.632+08:00 DEBUG 11032 --- [http-nio-8889-exec-13] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:37:46.632+08:00  INFO 11032 --- [http-nio-8889-exec-13] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:37:46.632+08:00  INFO 11032 --- [http-nio-8889-exec-13] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:37:47.296+08:00  INFO 11032 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:37:47.613+08:00  INFO 11032 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:37:47.614+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:38:08.060+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:38:08.060+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:38:08.061+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:38:08.061+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:38:08.063+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:38:08.063+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:38:08.064+08:00 DEBUG 11032 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:38:08.065+08:00  INFO 11032 --- [http-nio-8889-exec-3] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:38:08.065+08:00  INFO 11032 --- [http-nio-8889-exec-3] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:38:08.065+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:38:08.065+08:00  INFO 11032 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:38:08.065+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:38:08.092+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:38:08.099+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:38:08.148+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:38:08.148+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:38:08.148+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:38:08.149+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:38:08.150+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:38:08.151+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:38:08.151+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-08-22T09:38:08.200+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到1条待接单工单
2025-08-22T09:38:08.239+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:38:08.323+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:38:08.325+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:38:08.465+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:38:08.549+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:38:08.552+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:38:08.678+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:38:08.778+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:38:08.778+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:38:08.778+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:38:08.910+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:38:09.005+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:38:09.111+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:38:09.111+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:38:09.161+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:38:09.235+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:38:09.272+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:38:09.357+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:38:09.467+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:38:09.467+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:38:09.572+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:38:09.572+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:39:17.140+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:39:17.140+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:17.140+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:17.142+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:39:17.142+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:39:17.142+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:39:17.212+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:39:17.435+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:39:17.543+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:39:17.543+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:17.543+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:17.544+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:39:17.545+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:17.545+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:17.545+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:39:17.546+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:39:17.546+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:39:17.546+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:39:17.545+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:39:17.547+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:17.547+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:17.547+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:39:17.547+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:17.547+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:17.548+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:39:17.549+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:17.549+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:17.549+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:39:17.549+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:39:17.549+08:00  INFO 11032 --- [http-nio-8889-exec-16] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:39:17.549+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:39:17.550+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:39:17.551+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:39:17.551+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:39:17.551+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:39:27.351+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:39:27.352+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:27.352+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:27.354+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/plans/messages?uid=6&role=admin
2025-08-22T09:39:27.355+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.heating.controller.PatrolController    : 获取巡检提醒消息: uid=6, role=admin
2025-08-22T09:39:27.355+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息开始: userId=6, role=admin
2025-08-22T09:39:27.383+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:39:27.386+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:39:27.424+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 管理员角色，查询到7个待执行或已超时的巡检记录
2025-08-22T09:39:27.548+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:39:27.549+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:27.549+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:27.549+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:39:27.549+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:27.550+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:27.551+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:39:27.551+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:39:27.551+08:00  INFO 11032 --- [http-nio-8889-exec-11] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:39:27.551+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:39:27.551+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:39:27.552+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:39:27.552+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:39:27.608+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:39:27.638+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 40, 状态: overdue
2025-08-22T09:39:27.838+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:39:27.853+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 20, 名称: 日计划0620, 记录ID: 41, 状态: overdue
2025-08-22T09:39:28.069+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:39:28.069+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 42, 状态: overdue
2025-08-22T09:39:28.271+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 43, 状态: overdue
2025-08-22T09:39:28.286+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:39:28.506+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:39:28.506+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:39:28.519+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 15, 名称: 换热站周检, 记录ID: 44, 状态: overdue
2025-08-22T09:39:28.752+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 45, 状态: overdue
2025-08-22T09:39:28.782+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:39:28.951+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 添加巡检消息: 计划ID: 21, 名称: 巡检日计划, 记录ID: 46, 状态: overdue
2025-08-22T09:39:28.951+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 获取巡检消息完成，共7条消息
2025-08-22T09:39:28.957+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:39:28.957+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:28.957+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:28.959+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/alarm/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:39:28.960+08:00  INFO 11032 --- [http-nio-8889-exec-6] com.heating.controller.AlarmController   : 获取告警消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:39:28.960+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 开始获取告警消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:39:28.995+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.service.impl.AlarmServiceImpl  : 获取告警消息列表成功，共0条
2025-08-22T09:39:29.000+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:39:29.000+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:29.000+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:29.002+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:39:29.003+08:00  INFO 11032 --- [http-nio-8889-exec-17] com.heating.controller.FaultController   : 获取故障消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:39:29.003+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.heating.service.impl.FaultServiceImpl  : 开始获取故障消息列表: userId=6, role=admin, heatUnitId=0
2025-08-22T09:39:29.023+08:00  INFO 11032 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:39:29.042+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:39:29.042+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:39:29.042+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:39:29.044+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/messages?uid=6&role=admin&heatUnitId=0
2025-08-22T09:39:29.045+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单消息: uid=6, role=admin, heatUnitId=0
2025-08-22T09:39:29.045+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单消息（待接单状态的工单）: userId=6, role=admin, heatUnitId=0
2025-08-22T09:39:29.078+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 获取工单消息成功，共找到1条待接单工单
2025-08-22T09:40:28.371+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T09:40:28.372+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:28.372+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:28.373+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T09:40:28.374+08:00  INFO 11032 --- [http-nio-8889-exec-16] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T09:40:28.374+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T09:40:28.374+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T09:40:30.000+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:40:30.001+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:30.001+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:30.001+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:40:30.001+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:30.001+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:30.002+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:40:30.002+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:30.002+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:30.003+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:40:30.003+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:40:30.003+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:40:30.003+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:30.003+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:30.003+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:40:30.003+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:40:30.004+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:40:30.004+08:00  INFO 11032 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:40:30.004+08:00  INFO 11032 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:40:30.004+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:40:30.005+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:30.005+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:30.005+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:40:30.006+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:40:30.007+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:40:30.007+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:40:30.007+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:40:30.073+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:40:30.665+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:40:30.665+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:30.665+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:30.667+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:40:30.667+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-22T09:40:30.667+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:40:30.736+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:40:31.393+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:40:32.050+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T09:40:32.050+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:32.050+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:32.052+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T09:40:32.053+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=待接单, uid=6, limit=null, page=1, pageSize=10, type=pending, heatUnitId=0
2025-08-22T09:40:32.053+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:40:32.162+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=1, 总页数=1
2025-08-22T09:40:33.445+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:40:33.446+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:33.446+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:33.448+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:40:33.449+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-22T09:40:33.449+08:00 DEBUG 11032 --- [http-nio-8889-exec-16] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:40:33.562+08:00  INFO 11032 --- [http-nio-8889-exec-16] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:40:34.059+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T09:40:34.060+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:40:34.060+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:40:34.063+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T09:40:34.065+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=待接单, uid=6, limit=null, page=1, pageSize=10, type=pending, heatUnitId=0
2025-08-22T09:40:34.065+08:00 DEBUG 11032 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:40:34.181+08:00  INFO 11032 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=1, 总页数=1
2025-08-22T09:41:15.559+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:41:15.559+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:41:15.559+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:41:15.559+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:41:15.559+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:41:15.559+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:41:15.559+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:41:15.560+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:41:15.560+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:41:15.561+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:41:15.561+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:41:15.562+08:00 DEBUG 11032 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:41:15.562+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:41:15.562+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:41:15.562+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:41:15.562+08:00  INFO 11032 --- [http-nio-8889-exec-15] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:41:15.562+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:41:15.562+08:00 DEBUG 11032 --- [http-nio-8889-exec-11] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:41:15.562+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:41:15.562+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:41:15.562+08:00  INFO 11032 --- [http-nio-8889-exec-15] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:41:15.561+08:00 DEBUG 11032 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:41:15.564+08:00 DEBUG 11032 --- [http-nio-8889-exec-18] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:41:15.564+08:00 DEBUG 11032 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:41:15.564+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:41:15.564+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:41:15.564+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:41:17.128+08:00  INFO 11032 --- [http-nio-8889-exec-11] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:41:18.420+08:00  INFO 11032 --- [http-nio-8889-exec-18] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:41:18.470+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:41:18.470+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:41:18.470+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:41:18.473+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:41:18.473+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-22T09:41:18.473+08:00 DEBUG 11032 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:41:23.938+08:00  INFO 11032 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:41:25.606+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T09:41:25.607+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:41:25.607+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:41:25.609+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T09:41:25.609+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=待接单, uid=6, limit=null, page=1, pageSize=10, type=pending, heatUnitId=0
2025-08-22T09:41:25.609+08:00 DEBUG 11032 --- [http-nio-8889-exec-17] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:41:35.735+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T09:41:35.735+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:41:35.735+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:41:37.045+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T09:41:37.047+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=待接单, uid=6, limit=null, page=1, pageSize=10, type=pending, heatUnitId=0
2025-08-22T09:41:37.047+08:00 DEBUG 11032 --- [http-nio-8889-exec-8] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:41:39.975+08:00  INFO 11032 --- [http-nio-8889-exec-8] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=1, 总页数=1
2025-08-22T09:41:43.783+08:00  INFO 11032 --- [http-nio-8889-exec-17] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=1, 总页数=1
2025-08-22T09:42:15.443+08:00  INFO 11032 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:42:15.445+08:00  INFO 11032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-22T09:42:15.626+08:00  INFO 11032 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-22T09:42:21.931+08:00  INFO 8824 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 8824 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T09:42:21.933+08:00 DEBUG 8824 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T09:42:21.933+08:00  INFO 8824 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T09:42:22.552+08:00  INFO 8824 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T09:42:22.659+08:00  INFO 8824 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 102 ms. Found 34 JPA repository interfaces.
2025-08-22T09:42:23.182+08:00  INFO 8824 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T09:42:23.191+08:00  INFO 8824 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T09:42:23.192+08:00  INFO 8824 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T09:42:23.238+08:00  INFO 8824 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T09:42:23.238+08:00  INFO 8824 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1262 ms
2025-08-22T09:42:23.291+08:00 DEBUG 8824 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T09:42:23.386+08:00  INFO 8824 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T09:42:23.434+08:00  INFO 8824 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T09:42:23.461+08:00  INFO 8824 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T09:42:23.545+08:00  INFO 8824 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T09:42:28.921+08:00  INFO 8824 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@70c31b17
2025-08-22T09:42:28.923+08:00  INFO 8824 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T09:42:29.208+08:00  INFO 8824 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T09:42:30.375+08:00  INFO 8824 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T09:42:31.284+08:00  INFO 8824 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:42:31.480+08:00  INFO 8824 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T09:42:33.112+08:00  INFO 8824 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-22T09:42:33.197+08:00  INFO 8824 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-08-22T09:42:33.236+08:00  INFO 8824 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5058430, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@445ba903, org.springframework.security.web.context.SecurityContextHolderFilter@7f93f7fd, org.springframework.security.web.header.HeaderWriterFilter@2c9ecb60, org.springframework.web.filter.CorsFilter@5cd59de1, org.springframework.security.web.authentication.logout.LogoutFilter@31324803, com.heating.filter.JwtAuthenticationFilter@1ee5632d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@739ceb17, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@61a74188, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@408ec4f6, org.springframework.security.web.access.ExceptionTranslationFilter@1b60f2c, org.springframework.security.web.access.intercept.AuthorizationFilter@2568cc21]
2025-08-22T09:42:33.515+08:00  INFO 8824 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-22T09:42:33.522+08:00  INFO 8824 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 11.925 seconds (process running for 12.51)
2025-08-22T09:42:34.575+08:00  INFO 8824 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22T09:42:34.576+08:00  INFO 8824 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-22T09:42:34.577+08:00  INFO 8824 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-22T09:42:34.590+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:42:34.604+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:42:34.604+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:42:34.653+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:42:34.672+08:00  INFO 8824 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-22T09:42:34.673+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:42:38.008+08:00  INFO 8824 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:42:39.324+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:42:39.325+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:42:39.325+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:42:39.326+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:42:39.326+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:42:39.327+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:42:39.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:42:39.333+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:42:39.335+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:42:39.336+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:42:39.337+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:42:39.337+08:00  INFO 8824 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:42:39.337+08:00  INFO 8824 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:42:39.336+08:00  INFO 8824 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:42:39.337+08:00  INFO 8824 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:42:39.337+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:42:39.338+08:00  INFO 8824 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:42:39.338+08:00  INFO 8824 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:42:39.344+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:42:41.270+08:00  INFO 8824 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:42:43.574+08:00  INFO 8824 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:44:03.327+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T09:44:03.328+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:03.328+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:03.330+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T09:44:03.332+08:00  INFO 8824 --- [http-nio-8889-exec-2] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T09:44:03.333+08:00  INFO 8824 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T09:44:03.333+08:00  INFO 8824 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T09:44:04.832+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/1
2025-08-22T09:44:04.832+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:04.832+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:04.835+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/1
2025-08-22T09:44:04.836+08:00  INFO 8824 --- [http-nio-8889-exec-5] com.heating.controller.FaultController   : 获取故障详情: id=1
2025-08-22T09:44:04.837+08:00  INFO 8824 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T09:44:04.837+08:00  INFO 8824 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 1
2025-08-22T09:44:05.011+08:00  INFO 8824 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 1
2025-08-22T09:44:05.011+08:00  INFO 8824 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 故障状态为已确认，获取关联工单信息
2025-08-22T09:44:05.641+08:00  WARN 8824 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 获取操作人信息失败: A TupleBackedMap cannot be modified
2025-08-22T09:44:06.020+08:00  INFO 8824 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 成功获取工单信息: id=1, status=待接单
2025-08-22T09:44:06.020+08:00  INFO 8824 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T09:44:06.020+08:00  INFO 8824 --- [http-nio-8889-exec-5] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images, operation_logs, work_order]
2025-08-22T09:44:06.023+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/1
2025-08-22T09:44:06.024+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:06.024+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:06.027+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/1
2025-08-22T09:44:06.028+08:00  INFO 8824 --- [http-nio-8889-exec-4] com.heating.controller.FaultController   : 获取故障详情: id=1
2025-08-22T09:44:06.028+08:00  INFO 8824 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T09:44:06.028+08:00  INFO 8824 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 1
2025-08-22T09:44:06.292+08:00  INFO 8824 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 1
2025-08-22T09:44:06.292+08:00  INFO 8824 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 故障状态为已确认，获取关联工单信息
2025-08-22T09:44:06.867+08:00  WARN 8824 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 获取操作人信息失败: A TupleBackedMap cannot be modified
2025-08-22T09:44:07.196+08:00  INFO 8824 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 成功获取工单信息: id=1, status=待接单
2025-08-22T09:44:07.197+08:00  INFO 8824 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T09:44:07.197+08:00  INFO 8824 --- [http-nio-8889-exec-4] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images, operation_logs, work_order]
2025-08-22T09:44:11.861+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:44:11.861+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:44:11.861+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:11.861+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:11.861+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:11.861+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:11.863+08:00 DEBUG 8824 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:44:11.863+08:00 DEBUG 8824 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:11.863+08:00 DEBUG 8824 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:11.865+08:00 DEBUG 8824 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:44:11.865+08:00 DEBUG 8824 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:11.865+08:00 DEBUG 8824 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:11.866+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:44:11.866+08:00 DEBUG 8824 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:44:11.866+08:00 DEBUG 8824 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:11.866+08:00 DEBUG 8824 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:11.866+08:00  INFO 8824 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:44:11.866+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:44:11.866+08:00 DEBUG 8824 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:44:11.864+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:44:11.866+08:00  INFO 8824 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:44:11.867+08:00  INFO 8824 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:44:11.867+08:00 DEBUG 8824 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:44:11.869+08:00 DEBUG 8824 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:44:11.869+08:00  INFO 8824 --- [http-nio-8889-exec-7] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:44:11.870+08:00  INFO 8824 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:44:11.870+08:00  INFO 8824 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:44:13.802+08:00  INFO 8824 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:44:16.121+08:00  INFO 8824 --- [http-nio-8889-exec-7] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:44:24.233+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:44:24.234+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:24.234+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:24.238+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T09:44:24.240+08:00  INFO 8824 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-22T09:44:24.240+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:44:24.479+08:00  INFO 8824 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:44:25.280+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:44:25.280+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:25.280+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:25.281+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:44:25.281+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:25.281+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:25.282+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:44:25.282+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:25.282+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:25.284+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:44:25.284+08:00 DEBUG 8824 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:44:25.284+08:00 DEBUG 8824 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:44:25.284+08:00 DEBUG 8824 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:44:25.284+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:25.284+08:00 DEBUG 8824 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:25.284+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:25.284+08:00 DEBUG 8824 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:25.284+08:00  INFO 8824 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:44:25.284+08:00  INFO 8824 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:44:25.285+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:44:25.285+08:00  INFO 8824 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:44:25.285+08:00 DEBUG 8824 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:44:25.287+08:00 DEBUG 8824 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:44:55.702+08:00  INFO 8824 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:44:25.287+08:00 DEBUG 8824 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:44:55.702+08:00  INFO 8824 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:44:55.702+08:00  INFO 8824 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:44:55.703+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:44:55.703+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.703+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.704+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:44:55.704+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.704+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.705+08:00 DEBUG 8824 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:44:55.706+08:00 DEBUG 8824 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.706+08:00 DEBUG 8824 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.706+08:00 DEBUG 8824 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:44:55.706+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:44:55.706+08:00  INFO 8824 --- [http-nio-8889-exec-6] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:44:55.706+08:00  INFO 8824 --- [http-nio-8889-exec-6] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:44:55.706+08:00 DEBUG 8824 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:44:55.706+08:00  INFO 8824 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:44:55.707+08:00 DEBUG 8824 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:44:55.707+08:00 DEBUG 8824 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.707+08:00 DEBUG 8824 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.707+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:44:55.707+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.707+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.708+08:00 DEBUG 8824 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:44:55.708+08:00 DEBUG 8824 --- [http-nio-8889-exec-12] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:44:55.708+08:00 DEBUG 8824 --- [http-nio-8889-exec-12] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.709+08:00 DEBUG 8824 --- [http-nio-8889-exec-12] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.709+08:00 DEBUG 8824 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:44:55.709+08:00 DEBUG 8824 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:44:55.710+08:00 DEBUG 8824 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:44:55.710+08:00 DEBUG 8824 --- [http-nio-8889-exec-13] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:44:55.710+08:00  INFO 8824 --- [http-nio-8889-exec-10] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:44:55.710+08:00  INFO 8824 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:44:55.710+08:00 DEBUG 8824 --- [http-nio-8889-exec-13] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.729+08:00 DEBUG 8824 --- [http-nio-8889-exec-14] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:44:55.710+08:00  INFO 8824 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:44:55.731+08:00 DEBUG 8824 --- [http-nio-8889-exec-13] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.733+08:00 DEBUG 8824 --- [http-nio-8889-exec-14] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.733+08:00 DEBUG 8824 --- [http-nio-8889-exec-14] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.734+08:00 DEBUG 8824 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.734+08:00 DEBUG 8824 --- [http-nio-8889-exec-11] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.729+08:00 DEBUG 8824 --- [http-nio-8889-exec-12] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:44:55.730+08:00 DEBUG 8824 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:44:55.738+08:00 DEBUG 8824 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:44:55.738+08:00 DEBUG 8824 --- [http-nio-8889-exec-15] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:44:55.739+08:00  INFO 8824 --- [http-nio-8889-exec-12] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:44:55.739+08:00  INFO 8824 --- [http-nio-8889-exec-12] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:44:57.163+08:00  WARN 8824 --- [MyHikariPool housekeeper] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Thread starvation or clock leap detected (housekeeper delta=58s127ms801µs200ns).
2025-08-22T09:44:57.165+08:00 DEBUG 8824 --- [http-nio-8889-exec-11] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:44:57.165+08:00 DEBUG 8824 --- [http-nio-8889-exec-14] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:44:57.166+08:00 DEBUG 8824 --- [http-nio-8889-exec-15] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:44:57.166+08:00  INFO 8824 --- [http-nio-8889-exec-11] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:44:57.166+08:00 DEBUG 8824 --- [http-nio-8889-exec-11] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:44:57.166+08:00  INFO 8824 --- [http-nio-8889-exec-15] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:44:57.167+08:00  INFO 8824 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:44:57.167+08:00  INFO 8824 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:44:57.167+08:00 DEBUG 8824 --- [http-nio-8889-exec-13] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:44:59.334+08:00  INFO 8824 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:44:59.528+08:00  INFO 8824 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:44:59.573+08:00  INFO 8824 --- [http-nio-8889-exec-11] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:45:00.806+08:00  INFO 8824 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:45:01.193+08:00  INFO 8824 --- [http-nio-8889-exec-10] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:45:01.193+08:00  INFO 8824 --- [http-nio-8889-exec-15] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:45:08.396+08:00  INFO 8824 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:45:08.397+08:00  INFO 8824 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-22T09:45:08.931+08:00  INFO 8824 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-22T09:45:14.975+08:00  INFO 21604 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 21604 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T09:45:14.976+08:00 DEBUG 21604 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T09:45:14.977+08:00  INFO 21604 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T09:45:15.596+08:00  INFO 21604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T09:45:15.704+08:00  INFO 21604 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 102 ms. Found 34 JPA repository interfaces.
2025-08-22T09:45:16.202+08:00  INFO 21604 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T09:45:16.211+08:00  INFO 21604 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T09:45:16.211+08:00  INFO 21604 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T09:45:16.261+08:00  INFO 21604 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T09:45:16.262+08:00  INFO 21604 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1255 ms
2025-08-22T09:45:16.318+08:00 DEBUG 21604 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T09:45:16.415+08:00  INFO 21604 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T09:45:16.469+08:00  INFO 21604 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T09:45:16.495+08:00  INFO 21604 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T09:45:16.582+08:00  INFO 21604 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T09:45:18.655+08:00  INFO 21604 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@64665781
2025-08-22T09:45:18.656+08:00  INFO 21604 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T09:45:18.985+08:00  INFO 21604 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T09:45:20.204+08:00  INFO 21604 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T09:45:20.845+08:00  INFO 21604 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:45:21.045+08:00  INFO 21604 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T09:45:22.067+08:00  WARN 21604 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
2025-08-22T09:45:22.068+08:00  INFO 21604 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:45:22.069+08:00  INFO 21604 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-22T09:45:22.296+08:00  INFO 21604 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-22T09:45:22.298+08:00  INFO 21604 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-22T09:45:22.305+08:00  INFO 21604 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-22T09:45:22.317+08:00 ERROR 21604 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343) ~[spring-boot-3.2.3.jar:3.2.3]
	at com.heating.HeatingApplication.main(HeatingApplication.java:11) ~[classes/:na]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 34 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1833) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782) ~[spring-beans-6.1.4.jar:6.1.4]
	... 44 common frames omitted
Caused by: java.lang.IllegalStateException: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.assertParameterNamesInAnnotatedQuery(JpaQueryMethod.java:179) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.<init>(JpaQueryMethod.java:147) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.DefaultJpaQueryMethodFactory.build(DefaultJpaQueryMethodFactory.java:44) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:94) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.3.jar:3.2.3]
	... 56 common frames omitted

2025-08-22T09:47:30.758+08:00  INFO 20660 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 20660 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T09:47:30.759+08:00 DEBUG 20660 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T09:47:30.759+08:00  INFO 20660 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T09:47:31.371+08:00  INFO 20660 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T09:47:31.480+08:00  INFO 20660 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 102 ms. Found 34 JPA repository interfaces.
2025-08-22T09:47:31.970+08:00  INFO 20660 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T09:47:31.978+08:00  INFO 20660 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T09:47:31.979+08:00  INFO 20660 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T09:47:32.025+08:00  INFO 20660 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T09:47:32.025+08:00  INFO 20660 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1234 ms
2025-08-22T09:47:32.087+08:00 DEBUG 20660 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T09:47:32.181+08:00  INFO 20660 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T09:47:32.228+08:00  INFO 20660 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T09:47:32.255+08:00  INFO 20660 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T09:47:32.339+08:00  INFO 20660 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T09:47:35.931+08:00  INFO 20660 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@5341641d
2025-08-22T09:47:35.931+08:00  INFO 20660 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T09:47:36.262+08:00  INFO 20660 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T09:47:37.422+08:00  INFO 20660 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T09:47:38.375+08:00  INFO 20660 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:47:38.565+08:00  INFO 20660 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T09:47:39.633+08:00  WARN 20660 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
2025-08-22T09:47:39.634+08:00  INFO 20660 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:47:39.635+08:00  INFO 20660 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-22T09:47:49.650+08:00  WARN 20660 --- [main] com.zaxxer.hikari.pool.HikariPool        : Timed-out waiting for close connection executor to shutdown
2025-08-22T09:47:49.650+08:00  INFO 20660 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-22T09:47:49.651+08:00  INFO 20660 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-22T09:47:49.659+08:00  INFO 20660 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-22T09:47:49.671+08:00 ERROR 20660 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343) ~[spring-boot-3.2.3.jar:3.2.3]
	at com.heating.HeatingApplication.main(HeatingApplication.java:11) ~[classes/:na]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 34 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1833) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782) ~[spring-beans-6.1.4.jar:6.1.4]
	... 44 common frames omitted
Caused by: java.lang.IllegalStateException: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.Long,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[status]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:orderNo IS NULL OR wo.order_no LIKE CONCAT('%', :orderNo, '%')) AND (:userId IS NULL OR wo.repair_user_id = :userId OR wo.transfer_user_id = :userId) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.assertParameterNamesInAnnotatedQuery(JpaQueryMethod.java:179) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.<init>(JpaQueryMethod.java:147) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.DefaultJpaQueryMethodFactory.build(DefaultJpaQueryMethodFactory.java:44) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:94) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.3.jar:3.2.3]
	... 56 common frames omitted

2025-08-22T09:48:32.647+08:00  INFO 8336 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 8336 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T09:48:32.648+08:00 DEBUG 8336 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T09:48:32.649+08:00  INFO 8336 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T09:48:33.205+08:00  INFO 8336 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T09:48:33.314+08:00  INFO 8336 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 103 ms. Found 34 JPA repository interfaces.
2025-08-22T09:48:33.795+08:00  INFO 8336 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T09:48:33.804+08:00  INFO 8336 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T09:48:33.804+08:00  INFO 8336 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T09:48:33.850+08:00  INFO 8336 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T09:48:33.850+08:00  INFO 8336 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1170 ms
2025-08-22T09:48:33.900+08:00 DEBUG 8336 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T09:48:33.993+08:00  INFO 8336 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T09:48:34.042+08:00  INFO 8336 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T09:48:34.069+08:00  INFO 8336 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T09:48:34.152+08:00  INFO 8336 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T09:48:35.176+08:00  INFO 8336 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@5402612e
2025-08-22T09:48:35.177+08:00  INFO 8336 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T09:48:35.466+08:00  INFO 8336 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T09:48:36.613+08:00  INFO 8336 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T09:48:37.307+08:00  INFO 8336 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:48:37.494+08:00  INFO 8336 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T09:48:39.081+08:00  INFO 8336 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-22T09:48:39.165+08:00  INFO 8336 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-08-22T09:48:39.202+08:00  INFO 8336 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@77da004a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@ddcd9f1, org.springframework.security.web.context.SecurityContextHolderFilter@393baef, org.springframework.security.web.header.HeaderWriterFilter@6f1c19d, org.springframework.web.filter.CorsFilter@31324803, org.springframework.security.web.authentication.logout.LogoutFilter@bb39957, com.heating.filter.JwtAuthenticationFilter@1e40fbb3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@19e15d63, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2764ad8a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4de621e3, org.springframework.security.web.access.ExceptionTranslationFilter@605f023b, org.springframework.security.web.access.intercept.AuthorizationFilter@66bd6746]
2025-08-22T09:48:39.447+08:00  INFO 8336 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-22T09:48:39.455+08:00  INFO 8336 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 7.115 seconds (process running for 7.611)
2025-08-22T09:48:58.372+08:00  INFO 8336 --- [http-nio-8889-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22T09:48:58.372+08:00  INFO 8336 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-22T09:48:58.373+08:00  INFO 8336 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-22T09:48:58.385+08:00 DEBUG 8336 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-22T09:48:58.398+08:00 DEBUG 8336 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:48:58.398+08:00 DEBUG 8336 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:48:58.447+08:00 DEBUG 8336 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-22T09:48:58.467+08:00  INFO 8336 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单统计数据: uid=6, heatUnitId=0
2025-08-22T09:48:58.468+08:00  INFO 8336 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单统计数据: userId=6, heatUnitId=0
2025-08-22T09:48:58.669+08:00  INFO 8336 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 用户角色: admin
2025-08-22T09:48:58.669+08:00  INFO 8336 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 管理员或主管角色统计逻辑
2025-08-22T09:48:58.835+08:00  INFO 8336 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 工单统计数据获取成功: 我的工单数=0, 待处理工单数=0, 待接单工单数=1
2025-08-22T09:48:58.931+08:00 DEBUG 8336 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:48:58.932+08:00 DEBUG 8336 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:48:58.932+08:00 DEBUG 8336 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:48:58.932+08:00 DEBUG 8336 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T09:48:58.933+08:00 DEBUG 8336 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:48:58.933+08:00 DEBUG 8336 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:48:58.933+08:00 DEBUG 8336 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T09:48:58.933+08:00 DEBUG 8336 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:48:58.933+08:00 DEBUG 8336 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:48:58.937+08:00 DEBUG 8336 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T09:48:58.937+08:00 DEBUG 8336 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:48:58.937+08:00 DEBUG 8336 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:48:58.937+08:00 DEBUG 8336 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T09:48:58.938+08:00 DEBUG 8336 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T09:48:58.938+08:00 DEBUG 8336 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:48:58.939+08:00 DEBUG 8336 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T09:48:58.939+08:00 DEBUG 8336 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T09:48:58.940+08:00 DEBUG 8336 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T09:48:58.942+08:00 DEBUG 8336 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T09:48:58.942+08:00 DEBUG 8336 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T09:48:58.942+08:00  INFO 8336 --- [http-nio-8889-exec-4] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T09:48:58.942+08:00  INFO 8336 --- [http-nio-8889-exec-4] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T09:48:58.943+08:00  INFO 8336 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T09:48:58.943+08:00  INFO 8336 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T09:48:58.945+08:00  INFO 8336 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T09:48:58.945+08:00  INFO 8336 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T09:48:58.945+08:00 DEBUG 8336 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T09:48:59.032+08:00  INFO 8336 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T09:49:01.187+08:00  INFO 8336 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T09:50:28.506+08:00  INFO 8336 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:50:28.507+08:00  INFO 8336 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-22T09:50:37.311+08:00  INFO 14744 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 14744 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T09:50:37.313+08:00 DEBUG 14744 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T09:50:37.313+08:00  INFO 14744 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T09:50:37.908+08:00  INFO 14744 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T09:50:38.019+08:00  INFO 14744 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 104 ms. Found 34 JPA repository interfaces.
2025-08-22T09:50:38.541+08:00  INFO 14744 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T09:50:38.550+08:00  INFO 14744 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T09:50:38.550+08:00  INFO 14744 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T09:50:38.597+08:00  INFO 14744 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T09:50:38.597+08:00  INFO 14744 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1251 ms
2025-08-22T09:50:38.651+08:00 DEBUG 14744 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T09:50:38.751+08:00  INFO 14744 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T09:50:38.803+08:00  INFO 14744 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T09:50:38.830+08:00  INFO 14744 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T09:50:38.913+08:00  INFO 14744 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T09:50:43.389+08:00  INFO 14744 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@5072e638
2025-08-22T09:50:43.390+08:00  INFO 14744 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T09:50:43.714+08:00  INFO 14744 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T09:50:45.035+08:00  INFO 14744 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T09:50:45.811+08:00  INFO 14744 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:50:46.006+08:00  INFO 14744 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T09:50:47.004+08:00  WARN 14744 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
2025-08-22T09:50:47.004+08:00  INFO 14744 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:50:47.005+08:00  INFO 14744 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-22T09:50:47.198+08:00  INFO 14744 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-22T09:50:47.200+08:00  INFO 14744 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-22T09:50:47.207+08:00  INFO 14744 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-22T09:50:47.222+08:00 ERROR 14744 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343) ~[spring-boot-3.2.3.jar:3.2.3]
	at com.heating.HeatingApplication.main(HeatingApplication.java:11) ~[classes/:na]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 34 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1833) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782) ~[spring-beans-6.1.4.jar:6.1.4]
	... 44 common frames omitted
Caused by: java.lang.IllegalStateException: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.assertParameterNamesInAnnotatedQuery(JpaQueryMethod.java:179) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.<init>(JpaQueryMethod.java:147) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.DefaultJpaQueryMethodFactory.build(DefaultJpaQueryMethodFactory.java:44) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:94) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.3.jar:3.2.3]
	... 56 common frames omitted

2025-08-22T09:51:57.870+08:00  INFO 8564 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 8564 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T09:51:57.871+08:00 DEBUG 8564 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T09:51:57.872+08:00  INFO 8564 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T09:51:58.502+08:00  INFO 8564 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T09:51:58.613+08:00  INFO 8564 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 105 ms. Found 34 JPA repository interfaces.
2025-08-22T09:51:59.211+08:00  INFO 8564 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T09:51:59.221+08:00  INFO 8564 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T09:51:59.221+08:00  INFO 8564 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T09:51:59.272+08:00  INFO 8564 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T09:51:59.272+08:00  INFO 8564 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1353 ms
2025-08-22T09:51:59.323+08:00 DEBUG 8564 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T09:51:59.418+08:00  INFO 8564 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T09:51:59.469+08:00  INFO 8564 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T09:51:59.497+08:00  INFO 8564 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T09:51:59.581+08:00  INFO 8564 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T09:52:00.690+08:00  INFO 8564 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@4fdac2a7
2025-08-22T09:52:00.691+08:00  INFO 8564 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T09:52:01.014+08:00  INFO 8564 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T09:52:02.257+08:00  INFO 8564 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T09:52:03.035+08:00  INFO 8564 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:52:03.219+08:00  INFO 8564 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T09:52:04.241+08:00  WARN 8564 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
2025-08-22T09:52:04.242+08:00  INFO 8564 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T09:52:04.243+08:00  INFO 8564 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-22T09:52:14.251+08:00  WARN 8564 --- [main] com.zaxxer.hikari.pool.HikariPool        : Timed-out waiting for close connection executor to shutdown
2025-08-22T09:52:14.251+08:00  INFO 8564 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-22T09:52:14.252+08:00  INFO 8564 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-22T09:52:14.259+08:00  INFO 8564 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-22T09:52:14.271+08:00 ERROR 8564 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343) ~[spring-boot-3.2.3.jar:3.2.3]
	at com.heating.HeatingApplication.main(HeatingApplication.java:11) ~[classes/:na]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 34 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1833) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782) ~[spring-beans-6.1.4.jar:6.1.4]
	... 44 common frames omitted
Caused by: java.lang.IllegalStateException: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, wo.repair_user_id as repairUserId, wo.transfer_user_id as transferUserId, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.assertParameterNamesInAnnotatedQuery(JpaQueryMethod.java:179) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.<init>(JpaQueryMethod.java:147) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.DefaultJpaQueryMethodFactory.build(DefaultJpaQueryMethodFactory.java:44) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:94) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.3.jar:3.2.3]
	... 56 common frames omitted

2025-08-22T10:09:08.010+08:00  INFO 20384 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 20384 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T10:09:08.011+08:00 DEBUG 20384 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T10:09:08.012+08:00  INFO 20384 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T10:09:08.619+08:00  INFO 20384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T10:09:08.739+08:00  INFO 20384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 114 ms. Found 34 JPA repository interfaces.
2025-08-22T10:09:09.240+08:00  INFO 20384 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T10:09:09.249+08:00  INFO 20384 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T10:09:09.250+08:00  INFO 20384 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T10:09:09.297+08:00  INFO 20384 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T10:09:09.297+08:00  INFO 20384 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1249 ms
2025-08-22T10:09:09.350+08:00 DEBUG 20384 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T10:09:09.447+08:00  INFO 20384 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T10:09:09.497+08:00  INFO 20384 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T10:09:09.524+08:00  INFO 20384 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T10:09:09.614+08:00  INFO 20384 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T10:09:11.107+08:00  INFO 20384 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@2ceb68a1
2025-08-22T10:09:11.109+08:00  INFO 20384 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T10:09:11.378+08:00  INFO 20384 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T10:09:12.776+08:00  INFO 20384 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T10:09:13.384+08:00  INFO 20384 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T10:09:13.578+08:00  INFO 20384 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T10:09:14.600+08:00  WARN 20384 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
2025-08-22T10:09:14.600+08:00  INFO 20384 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T10:09:14.601+08:00  INFO 20384 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
2025-08-22T10:09:14.804+08:00  INFO 20384 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown completed.
2025-08-22T10:09:14.805+08:00  INFO 20384 --- [main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-22T10:09:14.813+08:00  INFO 20384 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-22T10:09:14.826+08:00 ERROR 20384 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultController': Unsatisfied dependency expressed through field 'faultService': Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624) ~[spring-context-6.1.4.jar:6.1.4]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354) ~[spring-boot-3.2.3.jar:3.2.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343) ~[spring-boot-3.2.3.jar:3.2.3]
	at com.heating.HeatingApplication.main(HeatingApplication.java:11) ~[classes/:na]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'faultServiceImpl': Unsatisfied dependency expressed through field 'workOrderRepository': Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'workOrderRepository' defined in com.heating.repository.WorkOrderRepository defined in @EnableJpaRepositories declared on JpaRepositoriesRegistrar.EnableJpaRepositoriesConfiguration: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1786) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784) ~[spring-beans-6.1.4.jar:6.1.4]
	... 34 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String); Reason: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1833) ~[spring-beans-6.1.4.jar:6.1.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1782) ~[spring-beans-6.1.4.jar:6.1.4]
	... 44 common frames omitted
Caused by: java.lang.IllegalStateException: Using named parameters for method public abstract java.util.List com.heating.repository.WorkOrderRepository.findWorkOrderListWithLimit(java.util.Date,java.lang.String,java.lang.String,java.lang.Integer,java.lang.String) but parameter 'Optional[orderNo]' not found in annotated query 'SELECT wo.id as orderId, wo.order_no as orderNo, h.name as heatUnitName, f.fault_type as faultType, f.fault_level as faultLevel, wo.order_status as orderStatus, DATE_FORMAT(wo.created_at, '%Y-%m-%d %H:%i') as createdTime FROM t_work_order wo LEFT JOIN t_fault f ON f.id = wo.fault_id LEFT JOIN t_heat_unit h ON f.heat_unit_id = h.id WHERE 1=1 AND (:date IS NULL OR DATE(wo.created_at) = :date) AND (:status IS NULL OR wo.order_status = :status) AND (:heatUnitId IS NULL OR     FIND_IN_SET(CAST(f.heat_unit_id AS CHAR), :heatUnitId) > 0 OR     :heatUnitId = '0') ORDER BY wo.created_at DESC LIMIT :limit'
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.assertParameterNamesInAnnotatedQuery(JpaQueryMethod.java:179) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryMethod.<init>(JpaQueryMethod.java:147) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.DefaultJpaQueryMethodFactory.build(DefaultJpaQueryMethodFactory.java:44) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:94) ~[spring-data-jpa-3.2.3.jar:3.2.3]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.3.jar:3.2.3]
	... 56 common frames omitted

2025-08-22T10:10:52.921+08:00  INFO 16648 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 16648 (E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\shaanxi_jieming_new_energy_company\4-Source\app\backend)
2025-08-22T10:10:52.922+08:00 DEBUG 16648 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-22T10:10:52.923+08:00  INFO 16648 --- [main] com.heating.HeatingApplication           : No active profile set, falling back to 1 default profile: "default"
2025-08-22T10:10:53.601+08:00  INFO 16648 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-22T10:10:53.712+08:00  INFO 16648 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 105 ms. Found 34 JPA repository interfaces.
2025-08-22T10:10:54.229+08:00  INFO 16648 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-22T10:10:54.237+08:00  INFO 16648 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-22T10:10:54.238+08:00  INFO 16648 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-22T10:10:54.287+08:00  INFO 16648 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-22T10:10:54.287+08:00  INFO 16648 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1328 ms
2025-08-22T10:10:54.344+08:00 DEBUG 16648 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-22T10:10:54.440+08:00  INFO 16648 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-22T10:10:54.489+08:00  INFO 16648 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-22T10:10:54.516+08:00  INFO 16648 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-22T10:10:54.610+08:00  INFO 16648 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-22T10:10:56.458+08:00  INFO 16648 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@5072e638
2025-08-22T10:10:56.460+08:00  INFO 16648 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-22T10:10:56.720+08:00  INFO 16648 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-22T10:10:57.900+08:00  INFO 16648 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-22T10:10:58.512+08:00  INFO 16648 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T10:10:58.698+08:00  INFO 16648 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-22T10:11:00.314+08:00  INFO 16648 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-22T10:11:00.399+08:00  INFO 16648 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/home/<USER>/work/web/uploads/
2025-08-22T10:11:00.438+08:00  INFO 16648 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7999114c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@770bdd86, org.springframework.security.web.context.SecurityContextHolderFilter@588e107d, org.springframework.security.web.header.HeaderWriterFilter@1110973, org.springframework.web.filter.CorsFilter@5371083b, org.springframework.security.web.authentication.logout.LogoutFilter@6726245e, com.heating.filter.JwtAuthenticationFilter@1e6060f1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1b60f2c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@43b3e62, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2568cc21, org.springframework.security.web.access.ExceptionTranslationFilter@2764ad8a, org.springframework.security.web.access.intercept.AuthorizationFilter@6a99a045]
2025-08-22T10:11:00.697+08:00  INFO 16648 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-22T10:11:00.706+08:00  INFO 16648 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 8.131 seconds (process running for 8.707)
2025-08-22T10:11:06.186+08:00  INFO 16648 --- [http-nio-8889-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22T10:11:06.186+08:00  INFO 16648 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-22T10:11:06.187+08:00  INFO 16648 --- [http-nio-8889-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-22T10:11:06.199+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-22T10:11:06.213+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:06.213+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:06.302+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/stats?uid=6&heatUnitId=0
2025-08-22T10:11:06.321+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单统计数据: uid=6, heatUnitId=0
2025-08-22T10:11:06.321+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 开始获取工单统计数据: userId=6, heatUnitId=0
2025-08-22T10:11:06.473+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 用户角色: admin
2025-08-22T10:11:06.473+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 管理员或主管角色统计逻辑
2025-08-22T10:11:06.581+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.h.service.impl.WorkOrderServiceImpl    : 工单统计数据获取成功: 我的工单数=0, 待处理工单数=0, 待接单工单数=1
2025-08-22T10:11:07.071+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:11:07.072+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:07.072+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:07.074+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:11:07.075+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:11:07.075+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:07.075+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:07.075+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:11:07.075+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:11:07.076+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:07.076+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:07.076+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:07.076+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:07.076+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:11:07.077+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:07.077+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:07.080+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:11:07.081+08:00  INFO 16648 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:11:07.081+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:11:07.082+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:11:07.082+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:11:07.083+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:11:07.084+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:11:07.085+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:11:07.085+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:11:07.088+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:11:07.088+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:11:07.159+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=1, 总页数=1
2025-08-22T10:11:08.586+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:11:23.332+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/list
2025-08-22T10:11:23.332+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:23.332+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:23.334+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/dict/data/5
2025-08-22T10:11:23.335+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:23.335+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/list
2025-08-22T10:11:23.335+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:23.335+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.controller.HeatUnitController  : Accessing GET /api/heatunits/list
2025-08-22T10:11:23.338+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/dict/data/5
2025-08-22T10:11:23.410+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.controller.HeatUnitController  : Retrieved 13 heat units
2025-08-22T10:11:41.579+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing POST /api/faults/report
2025-08-22T10:11:41.580+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:41.580+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:41.582+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured POST /api/faults/report
2025-08-22T10:11:41.604+08:00  INFO 16648 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 上报故障: FaultReportRequest(heatUnitId=1, alarmId=-1, faultType=管网故障, faultSource=用户投诉, faultLevel=一般, faultDesc=测试故障, faultStatus=null, occurTime=2025-08-22T10:11, reportUserId=6, repairtUserId=0, address=1-1-0106, managerId=null, attachment=[])
2025-08-22T10:11:41.713+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 找到匹配的住户ID: 6
2025-08-22T10:11:43.403+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:11:43.404+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:11:43.404+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:43.404+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:43.404+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:43.404+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:43.405+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:11:43.406+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:43.406+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:43.407+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:11:43.409+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:11:43.410+08:00  INFO 16648 --- [http-nio-8889-exec-1] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:11:43.410+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:11:43.407+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:11:43.412+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:43.412+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:43.414+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:11:43.416+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:11:43.416+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:11:43.416+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:11:43.418+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:11:43.418+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:43.419+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:43.421+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:11:43.422+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:11:43.422+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:11:43.422+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:11:43.493+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=1, 总页数=1
2025-08-22T10:11:44.737+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:11:46.416+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:46.417+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:46.417+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:46.419+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:46.420+08:00  INFO 16648 --- [http-nio-8889-exec-5] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:11:46.421+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:11:46.421+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:11:47.969+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:47.969+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:47.969+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:47.972+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:47.973+08:00  INFO 16648 --- [http-nio-8889-exec-6] com.heating.controller.FaultController   : 获取故障列表: status=待确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:11:47.973+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:11:47.973+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:11:48.720+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:48.721+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:48.721+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:48.724+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:48.725+08:00  INFO 16648 --- [http-nio-8889-exec-7] com.heating.controller.FaultController   : 获取故障列表: status=已确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:11:48.725+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:11:48.725+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:11:49.325+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%B7%B2%E9%80%80%E5%9B%9E&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:49.325+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:49.326+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:49.329+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%B7%B2%E9%80%80%E5%9B%9E&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:49.329+08:00  INFO 16648 --- [http-nio-8889-exec-8] com.heating.controller.FaultController   : 获取故障列表: status=已退回, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:11:49.330+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:11:49.330+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:11:49.961+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:49.962+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:49.962+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:49.964+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%B7%B2%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:49.965+08:00  INFO 16648 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 获取故障列表: status=已确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:11:49.965+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:11:49.965+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:11:50.279+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:50.279+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:50.280+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:50.282+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?status=%E5%BE%85%E7%A1%AE%E8%AE%A4&page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:50.283+08:00  INFO 16648 --- [http-nio-8889-exec-4] com.heating.controller.FaultController   : 获取故障列表: status=待确认, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:11:50.283+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:11:50.283+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:11:50.602+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:50.602+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:11:50.603+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:11:50.606+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:11:50.606+08:00  INFO 16648 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:11:50.606+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:11:50.606+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:12:01.451+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:12:01.451+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:12:01.451+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:12:01.454+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:12:01.455+08:00  INFO 16648 --- [http-nio-8889-exec-2] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:12:01.456+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:12:01.456+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:12:01.577+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:12:01.577+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:12:01.577+08:00  INFO 16648 --- [http-nio-8889-exec-2] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images]
2025-08-22T10:12:01.578+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:12:01.579+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:12:01.579+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:12:01.581+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:12:01.582+08:00  INFO 16648 --- [http-nio-8889-exec-1] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:12:01.582+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:12:01.582+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:12:01.691+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:12:01.691+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:12:01.691+08:00  INFO 16648 --- [http-nio-8889-exec-1] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images]
2025-08-22T10:13:04.428+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:13:04.429+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:13:04.429+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:13:04.431+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:13:04.432+08:00  INFO 16648 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:13:04.432+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:13:04.432+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:13:04.538+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:13:04.538+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:13:04.538+08:00  INFO 16648 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images]
2025-08-22T10:13:04.540+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:13:04.541+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:13:04.541+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:13:04.544+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:13:04.545+08:00  INFO 16648 --- [http-nio-8889-exec-4] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:13:04.545+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:13:04.545+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:13:04.653+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:13:04.654+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:13:04.654+08:00  INFO 16648 --- [http-nio-8889-exec-4] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images]
2025-08-22T10:13:06.311+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:13:06.312+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:13:06.312+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:13:06.314+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:13:06.315+08:00  INFO 16648 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:13:06.315+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:13:06.315+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:13:06.417+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:13:06.417+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:13:06.417+08:00  INFO 16648 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images]
2025-08-22T10:13:06.419+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:13:06.419+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:13:06.419+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:13:06.421+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:13:06.422+08:00  INFO 16648 --- [http-nio-8889-exec-2] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:13:06.422+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:13:06.422+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:13:06.523+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:13:06.523+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:13:06.523+08:00  INFO 16648 --- [http-nio-8889-exec-2] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images]
2025-08-22T10:14:00.234+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-22T10:14:00.235+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T10:14:00.258+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-22T10:14:00.258+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T10:14:00.266+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-22T10:14:00.266+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T10:14:00.266+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-22T10:14:00.266+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T10:14:02.083+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-22T10:14:02.084+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-22T10:14:02.415+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T10:14:02.419+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-22T10:14:02.420+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:02.420+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:02.423+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-22T10:14:03.477+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:14:03.477+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:14:03.478+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.478+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.478+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.478+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.479+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:14:03.479+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.479+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.480+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:14:03.480+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:14:03.480+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:14:03.480+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.480+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.481+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:14:03.481+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:14:03.481+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:14:03.481+08:00  INFO 16648 --- [http-nio-8889-exec-7] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:14:03.481+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:14:03.483+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:14:03.483+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:14:03.484+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.484+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.486+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:14:03.486+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:14:03.486+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:14:03.486+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:14:03.553+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=1, 总页数=1
2025-08-22T10:14:03.557+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:14:03.558+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:14:03.558+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.558+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.558+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.558+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.559+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:14:03.561+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:14:03.561+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:14:03.562+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:14:03.595+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=1, 总页数=1
2025-08-22T10:14:03.726+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:14:03.728+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.728+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.731+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:14:03.765+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:14:03.765+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:03.765+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:03.767+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:14:03.768+08:00  INFO 16648 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:14:03.768+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:14:04.664+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:14:04.665+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:04.665+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:04.667+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:14:04.668+08:00  INFO 16648 --- [http-nio-8889-exec-6] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:14:04.668+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:14:04.668+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:14:04.807+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:14:04.808+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:14:04.809+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:04.809+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:04.811+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:14:04.812+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:14:04.812+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:14:04.812+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:14:05.339+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:14:05.340+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:05.340+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:05.342+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:14:05.343+08:00  INFO 16648 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:14:05.343+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:14:05.343+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:14:05.454+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:14:05.454+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:14:05.454+08:00  INFO 16648 --- [http-nio-8889-exec-9] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images]
2025-08-22T10:14:05.456+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:14:05.456+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:05.456+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:05.459+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:14:05.459+08:00  INFO 16648 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:14:05.459+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:14:05.460+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:14:05.533+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:14:05.533+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:14:05.533+08:00  INFO 16648 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images]
2025-08-22T10:14:06.122+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:14:08.738+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/faults/status
2025-08-22T10:14:08.739+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:08.739+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:08.742+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/faults/status
2025-08-22T10:14:08.744+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : Confirming fault with ID: 2
2025-08-22T10:14:09.163+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.FaultServiceImpl  : 从请求中获取热用户ID: 1
2025-08-22T10:14:10.952+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:14:10.952+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:14:10.952+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:14:10.956+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:14:10.958+08:00  INFO 16648 --- [http-nio-8889-exec-8] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:14:10.958+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:14:10.958+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:15:49.462+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:15:49.462+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:15:49.462+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:15:49.464+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:15:49.465+08:00  INFO 16648 --- [http-nio-8889-exec-10] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:15:49.465+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:15:49.465+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:16:17.701+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing OPTIONS /api/auth/login
2025-08-22T10:16:17.701+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T10:16:17.702+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/api/auth/login?continue to session
2025-08-22T10:16:17.702+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T10:16:17.702+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing OPTIONS /error
2025-08-22T10:16:17.702+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T10:16:17.702+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.s.w.s.HttpSessionRequestCache        : Saved request http://127.0.0.1:8889/error?continue to session
2025-08-22T10:16:17.702+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.s.w.a.Http403ForbiddenEntryPoint     : Pre-authenticated entry point called. Rejecting access
2025-08-22T10:16:18.706+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing POST /api/auth/login
2025-08-22T10:16:18.707+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured POST /api/auth/login
2025-08-22T10:16:19.013+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-22T10:16:19.017+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/auth/user-info
2025-08-22T10:16:19.017+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:19.017+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:19.019+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/auth/user-info
2025-08-22T10:16:20.060+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:16:20.060+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.060+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.061+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:16:20.061+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.061+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.062+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:16:20.062+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:16:20.062+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.062+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.063+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:16:20.063+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:16:20.063+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:16:20.064+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:16:20.064+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:16:20.064+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.064+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.064+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.064+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.065+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:16:20.065+08:00  INFO 16648 --- [http-nio-8889-exec-1] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:16:20.065+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:16:20.066+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:16:20.066+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:16:20.066+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:16:20.066+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:16:20.066+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:16:20.129+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-08-22T10:16:20.132+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:16:20.132+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.132+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.134+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:16:20.134+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:16:20.134+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:16:20.157+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:16:20.157+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.157+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.159+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:16:20.168+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-08-22T10:16:20.293+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:16:20.294+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.294+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.296+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:16:20.334+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:16:20.334+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:20.335+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:20.336+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:16:20.337+08:00  INFO 16648 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:16:20.337+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:16:21.421+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:16:21.423+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:16:21.423+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:21.423+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:21.425+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:16:21.426+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:16:21.426+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:16:21.426+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:16:22.157+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T10:16:22.157+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:22.157+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:22.159+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&uid=6&type=my&heatUnitId=0
2025-08-22T10:16:22.160+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=null, page=1, pageSize=10, type=my, heatUnitId=0
2025-08-22T10:16:22.160+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:16:22.273+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=0, 总页数=0
2025-08-22T10:16:22.752+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:16:23.347+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T10:16:23.348+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:23.348+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:23.350+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?page=1&pageSize=10&status=%E5%BE%85%E6%8E%A5%E5%8D%95&uid=6&type=pending&heatUnitId=0
2025-08-22T10:16:23.351+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=待接单, uid=6, limit=null, page=1, pageSize=10, type=pending, heatUnitId=0
2025-08-22T10:16:23.351+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:16:23.452+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-08-22T10:16:24.909+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:16:24.910+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:16:24.910+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:24.911+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:24.911+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:24.911+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:16:24.911+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:24.911+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:24.911+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:24.913+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:16:24.913+08:00  INFO 16648 --- [http-nio-8889-exec-5] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:16:24.913+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:16:24.913+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:16:24.915+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:16:24.915+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:16:24.917+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:16:24.918+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:16:24.918+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:24.918+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:24.919+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:16:24.919+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:24.919+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:24.920+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:16:24.921+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:16:24.923+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:16:24.923+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:16:24.923+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:16:24.989+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-08-22T10:16:26.239+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:16:27.025+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/detail/1
2025-08-22T10:16:27.025+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:27.026+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:27.027+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/detail/1
2025-08-22T10:16:27.028+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.h.controller.WorkOrderController       : 获取工单详情: orderId=1
2025-08-22T10:16:36.077+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:16:36.077+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:36.077+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:36.077+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:16:36.078+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:36.078+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:36.079+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:16:36.079+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:36.079+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:36.079+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:16:36.080+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:36.080+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:36.081+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:16:36.081+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:16:36.081+08:00  INFO 16648 --- [http-nio-8889-exec-8] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:16:36.081+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:16:36.082+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:16:36.082+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:16:36.082+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:16:36.082+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:36.082+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:36.083+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:16:36.084+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:16:36.085+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:16:36.086+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:16:36.086+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:16:36.086+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:16:36.149+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-08-22T10:16:36.831+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/detail/2
2025-08-22T10:16:36.831+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:16:36.831+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:16:36.833+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/detail/2
2025-08-22T10:16:36.834+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单详情: orderId=2
2025-08-22T10:16:37.419+08:00  INFO 16648 --- [http-nio-8889-exec-9] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:17:00.399+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:17:00.399+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:00.399+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:00.400+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:17:00.400+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:00.400+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:00.400+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:17:00.401+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:00.401+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:00.401+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:17:00.402+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:17:00.402+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:00.402+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:00.402+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:17:00.402+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:17:00.403+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:17:00.403+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:00.403+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:00.403+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:17:00.403+08:00 DEBUG 16648 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:17:00.403+08:00  INFO 16648 --- [http-nio-8889-exec-3] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:17:00.403+08:00  INFO 16648 --- [http-nio-8889-exec-3] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:17:00.404+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:17:00.405+08:00 DEBUG 16648 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:17:00.405+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:17:00.405+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:17:00.405+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:17:00.479+08:00  INFO 16648 --- [http-nio-8889-exec-10] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-08-22T10:17:01.755+08:00  INFO 16648 --- [http-nio-8889-exec-6] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:17:02.168+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:17:02.169+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:02.169+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:02.170+08:00 DEBUG 16648 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/list?page=1&pageSize=5&heatUnitId=0
2025-08-22T10:17:02.171+08:00  INFO 16648 --- [http-nio-8889-exec-4] com.heating.controller.FaultController   : 获取故障列表: status=null, date=null, heatUnitId=0, page=1, pageSize=5
2025-08-22T10:17:02.171+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 根据用户项目权限筛选故障列表: heatUnitId=0
2025-08-22T10:17:02.171+08:00  INFO 16648 --- [http-nio-8889-exec-4] c.heating.service.impl.FaultServiceImpl  : 用户拥有全局权限，显示所有故障记录
2025-08-22T10:17:03.096+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:17:03.097+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:03.097+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:03.098+08:00 DEBUG 16648 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:17:03.099+08:00  INFO 16648 --- [http-nio-8889-exec-7] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:17:03.099+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:17:03.099+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:17:03.206+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:17:03.206+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 故障状态为已确认，获取关联工单信息
2025-08-22T10:17:03.512+08:00  WARN 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 获取操作人信息失败: A TupleBackedMap cannot be modified
2025-08-22T10:17:03.725+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : 成功获取工单信息: id=2, status=待接单
2025-08-22T10:17:03.726+08:00  INFO 16648 --- [http-nio-8889-exec-7] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:17:03.726+08:00  INFO 16648 --- [http-nio-8889-exec-7] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images, operation_logs, work_order]
2025-08-22T10:17:03.728+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/detail/2
2025-08-22T10:17:03.728+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:03.728+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:03.731+08:00 DEBUG 16648 --- [http-nio-8889-exec-8] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/detail/2
2025-08-22T10:17:03.731+08:00  INFO 16648 --- [http-nio-8889-exec-8] com.heating.controller.FaultController   : 获取故障详情: id=2
2025-08-22T10:17:03.731+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : === Fault Detail Debug ===
2025-08-22T10:17:03.731+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : Requested fault_id: 2
2025-08-22T10:17:03.831+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : Found 0 attachments for fault ID: 2
2025-08-22T10:17:03.831+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 故障状态为已确认，获取关联工单信息
2025-08-22T10:17:04.109+08:00  WARN 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 获取操作人信息失败: A TupleBackedMap cannot be modified
2025-08-22T10:17:04.306+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : 成功获取工单信息: id=2, status=待接单
2025-08-22T10:17:04.306+08:00  INFO 16648 --- [http-nio-8889-exec-8] c.heating.service.impl.FaultServiceImpl  : Returning data: images=0, video=false
2025-08-22T10:17:04.306+08:00  INFO 16648 --- [http-nio-8889-exec-8] com.heating.controller.FaultController   : 故障详情数据: [fault_info, images, operation_logs, work_order]
2025-08-22T10:17:09.682+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:17:09.682+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:09.682+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:09.682+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Securing GET /api/heatunits/count
2025-08-22T10:17:09.683+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:09.683+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:09.683+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Securing GET /api/hes/online-rate
2025-08-22T10:17:09.684+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured GET /api/WorkOrders/list?limit=3&uid=6&role=admin&heatUnitId=0
2025-08-22T10:17:09.684+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:09.684+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:09.684+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 获取工单列表: date=null, status=null, uid=6, limit=3, page=1, pageSize=10, type=null, heatUnitId=0
2025-08-22T10:17:09.684+08:00 DEBUG 16648 --- [http-nio-8889-exec-1] c.h.controller.WorkOrderController       : 开始查询工单列表...
2025-08-22T10:17:09.684+08:00 DEBUG 16648 --- [http-nio-8889-exec-9] o.s.security.web.FilterChainProxy        : Secured GET /api/heatunits/count
2025-08-22T10:17:09.685+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Securing GET /api/faults/weekly-count
2025-08-22T10:17:09.685+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:09.685+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:09.686+08:00 DEBUG 16648 --- [http-nio-8889-exec-2] o.s.security.web.FilterChainProxy        : Secured GET /api/hes/online-rate
2025-08-22T10:17:09.686+08:00  INFO 16648 --- [http-nio-8889-exec-2] com.heating.controller.HesController     : Accessing GET /api/hes/online-rate
2025-08-22T10:17:09.686+08:00  INFO 16648 --- [http-nio-8889-exec-2] c.heating.service.impl.HesServiceImpl    : 获取换热站在线率
2025-08-22T10:17:09.686+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Securing GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:17:09.687+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Extracting token from request...
2025-08-22T10:17:09.687+08:00 DEBUG 16648 --- [http-nio-8889-exec-10] o.s.security.web.FilterChainProxy        : Secured GET /api/faults/weekly-count
2025-08-22T10:17:09.687+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] c.h.filter.JwtAuthenticationFilter       : Token found in Authorization header.
2025-08-22T10:17:09.688+08:00 DEBUG 16648 --- [http-nio-8889-exec-5] o.s.security.web.FilterChainProxy        : Secured GET /api/patrols/records/limit?uid=6&role=admin&limit=3
2025-08-22T10:17:09.688+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.heating.controller.PatrolController    : 获取有限数量的巡检工单信息: uid=6, role=admin, limit=3
2025-08-22T10:17:09.688+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取有限数量的巡检工单信息: userId=6, role=admin, limit=3
2025-08-22T10:17:09.689+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 以管理员权限获取巡检工单
2025-08-22T10:17:09.758+08:00  INFO 16648 --- [http-nio-8889-exec-1] c.h.service.impl.WorkOrderServiceImpl    : 查询工单列表完成: 当前页=1, 每页数量=10, 总记录数=2, 总页数=1
2025-08-22T10:17:11.043+08:00  INFO 16648 --- [http-nio-8889-exec-5] c.h.service.impl.PatrolServiceImpl       : 获取到3条巡检工单记录
2025-08-22T10:53:47.281+08:00  INFO 16648 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-22T10:53:47.282+08:00  INFO 16648 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Shutdown initiated...
