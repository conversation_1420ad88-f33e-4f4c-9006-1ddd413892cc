
package com.heating.service.impl;

import com.heating.dto.*;
import com.heating.dto.user.LoginRequest;
import com.heating.dto.user.RegisterRequest;
import com.heating.dto.user.UserInfoUpdateRequest;
import com.heating.dto.user.WorkStats;
import com.heating.dto.user.UserListRequest;
import com.heating.dto.user.UserListResponse;
import com.heating.entity.attendance.TOtherParam;
import com.heating.entity.user.TUser;
import com.heating.repository.OtherSystemRepository;
import com.heating.repository.UserRepository;
import com.heating.service.AuthService;
import com.heating.utils.JwtUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {
    private final UserRepository userRepository;
    private final JwtUtils jwtUtils;
    private final PasswordEncoder passwordEncoder;
    private final OtherSystemRepository otherSystemRepository;

    @Autowired
    private EntityManager entityManager;

    @Override
    @Transactional
    public Map<String, Object> userLogin(LoginRequest request) {
        Optional<TUser> optionalUser = userRepository.findByUsername(request.username());
        if (optionalUser.isEmpty()) {
            return Map.of(
                "code", 400 ,
                "message", "用户不存在"
            );
        }
        TUser user = optionalUser.get();
        // 使用BCrypt密码验证
        if (!passwordEncoder.matches(request.password(), user.getPassword())) {
            return Map.of(
                "code", 400 ,    
                "message", "密码错误"
            );
        }
        // 判断是否审核通过
        if (user.getIsAudit() == null || user.getIsAudit().equals(0)) {
            return Map.of(
                "code", 400,
                "message", "该账号暂未审核,请等待管理员审核"
            );
        }
        // 判断用户是否被禁用
        if (user.getStatus() != null && user.getStatus().equals(0)) {
            return Map.of(
                "code", 400,
                "message", "该账号已被禁用，请联系管理员"
            );
        }
        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userRepository.save(user);
        
        // 获取权限时添加缓存
        List<String> permissions = getPermissionsByRole(user.getRole());
        user.setPermissions(permissions);
        String token = jwtUtils.generateToken(user);
        
        // 确保heatUnitId不为null，如果为null则设置为空字符串
        String heatUnitId = user.getHeatUnitId();
        if (heatUnitId == null) {
            heatUnitId = "";
        }

        // 获取定位上传周期参数，默认获取第一条记录
        Integer positionCycle = 1; // 默认值
        try {
            TOtherParam systemParams = otherSystemRepository.findById(1L);
            if (systemParams != null && systemParams.getPositionCycle() != null) {
                positionCycle = systemParams.getPositionCycle();
            }
        } catch (Exception e) {
            log.warn("获取定位上传周期参数失败，使用默认值: {}", e.getMessage());
        }

        return Map.of(
            "code", 200,
            "data", Map.of(
                "token", token,
                "userId", user.getId(),
                "role", user.getRole(),
                "isPositioning", user.getIsPositioning(),
                "permissions", user.getPermissions(),
                "heatUnitId", heatUnitId,
                "positionCycle", positionCycle
            )
        );
    }

    @Override
    @Transactional
    public Map<String, Object> phoneLogin(LoginRequest request) {
        // 通过手机号查找用户
        Optional<TUser> optionalUser = userRepository.findByPhone(request.username());
        if (optionalUser.isEmpty()) {
            return Map.of(
                "code", 400 ,
                "message", "该手机号未注册"
            );
        }
        TUser user = optionalUser.get();
        // 使用BCrypt密码验证
        if (!passwordEncoder.matches(request.password(), user.getPassword())) {
            return Map.of(
                "code", 400 ,    
                "message", "密码错误"
            );
        }
        // 判断是否审核通过
        if (user.getIsAudit() == null || user.getIsAudit().equals(0)) {
            return Map.of(
                "code", 400,
                "message", "该账号暂未审核,请等待管理员审核"
            );
        }
        // 判断用户是否被禁用
        if (user.getStatus() != null && user.getStatus().equals(0)) {
            return Map.of(
                "code", 400,
                "message", "该账号已被禁用，请联系管理员"
            );
        }
        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userRepository.save(user);
        
        // 获取权限时添加缓存
        List<String> permissions = getPermissionsByRole(user.getRole());
        user.setPermissions(permissions);
        String token = jwtUtils.generateToken(user);
        
        // 确保heatUnitId不为null，如果为null则设置为空字符串
        String heatUnitId = user.getHeatUnitId();
        if (heatUnitId == null) {
            heatUnitId = "";
        }

        // 获取定位上传周期参数，默认获取第一条记录
        Integer positionCycle = 1; // 默认值
        try {
            TOtherParam systemParams = otherSystemRepository.findById(1L);
            if (systemParams != null && systemParams.getPositionCycle() != null) {
                positionCycle = systemParams.getPositionCycle();
            }
        } catch (Exception e) {
            log.warn("获取定位上传周期参数失败，使用默认值: {}", e.getMessage());
        }

        return Map.of(
            "code", 200,
            "data", Map.of(
                "token", token,
                "userId", user.getId(),
                "role", user.getRole(),
                "isPositioning", user.getIsPositioning(),
                "permissions", user.getPermissions(),
                "heatUnitId", heatUnitId,
                "positionCycle", positionCycle
            )
        );
    }
 
    @Override
    public Map<String, Object> getUserInfo(Long userId) {
        Optional<TUser> optionalUser = userRepository.findById(userId);
        if (optionalUser.isEmpty()) {
            return Map.of(
                "code", 400 ,    
                "message", "用户不存在"
            );
        }
        TUser user = optionalUser.get();
        // 计算工作统计数据
        WorkStats workStats = calculateWorkStats(userId);
        user.setWorkStats(workStats); 
        // 获取用户权限
       List<String> permissions = getPermissionsByRole(user.getRole());
       user.setPermissions(permissions); 
        return Map.of(
            "code", 200,    
            "data", Map.of(
                "user", user
            )   
        );
    }

    @Override
    public WorkStats calculateWorkStats(Long userId) {
       
        return new WorkStats(
            1,2,3.0,LocalDateTime.now(),5
        );
    }

    @Override
    @Transactional
    public boolean updateUserInfo(Long userId, UserInfoUpdateRequest request) {
        try {
                Optional<TUser> optionalUser = userRepository.findById(userId);
                if(optionalUser.isEmpty()){
                    return false;
                }
            TUser user = optionalUser.get();
                if (request.getEmail() != null) {
                    user.setEmail(request.getEmail());
                }
                if (request.getName() != null) {
                    user.setName(request.getName());
                } 
                if (request.getPhone() != null) {
                    user.setPhone(request.getPhone());
                } 
                if (request.getAvatar() != null) {
                    user.setAvatar(request.getAvatar());
                }   
                if (request.getDepartment() != null) {
                    user.setDepartment(request.getDepartment());
                }
                if (request.getSkills() != null) {
                    user.setSkills(request.getSkills());
                }   
                if (request.getCertifications() != null) {
                    user.setCertifications(request.getCertifications());
                }   
                if (request.getWorkStats() != null) {
                    user.setWorkStats(request.getWorkStats());
                }
                user.setUpdateTime(LocalDateTime.now()); 
                userRepository.save(user);
                entityManager.flush();
                return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public List<String> getPermissionsByRole(String roleCode) {
        if (roleCode == null || roleCode.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 处理可能包含多个角色的情况，角色之间用逗号分隔
        String[] roleCodes = roleCode.split(",");
        List<String> allPermissions = new ArrayList<>();
        
        // 遍历所有角色，获取每个角色的权限并添加到结果列表中
        for (String code : roleCodes) {
            String trimmedCode = code.trim();
            if (!trimmedCode.isEmpty()) {
                List<String> permissions = userRepository.findPermissionsByRoleCode(trimmedCode);
                if (permissions != null) {
                    // 添加该角色的所有权限
                    allPermissions.addAll(permissions);
                }
            }
        }
        
        // 去重，确保权限不重复
        return allPermissions.stream().distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean userRegister(RegisterRequest request) {
        String username = request.username();
        String password = request.password();  
        String phone = request.phone();
        if (username == null || username.isBlank()) {
            return false;
        }
        if (phone == null || phone.isBlank()) {
            return false;
        }
        if (password == null || password.isBlank()) {
            return false;
        }
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.username())) {
            return false;
        } 
        // 检查手机号是否已存在
        if (userRepository.existsByPhone(request.phone())) {
            return false;
        }
        TUser user = new TUser();
        user.setUsername(request.username());
        user.setPassword(passwordEncoder.encode(request.password()));
        user.setName(request.name());
        user.setPhone(request.phone());
        user.setEnabled(true);
        // 设置默认审核状态为未审核(0)
        user.setIsAudit(0);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now()); 
        user = userRepository.save(user);
        return true;
    } 

    /**
     * 获取用户列表
     * @param request 用户列表请求，包含可选的角色筛选条件
     * @return 用户列表
     */
    @Override
    public List<UserListResponse> getUserList(UserListRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<TUser> query = cb.createQuery(TUser.class);
        Root<TUser> root = query.from(TUser.class);
        
        List<Predicate> predicates = new ArrayList<>();
        
        // 添加角色过滤条件 - 改为模糊匹配
        if (request != null && StringUtils.hasText(request.getRole())) {
            String rolePattern = "%" + request.getRole() + "%";
            predicates.add(cb.like(root.get("role"), rolePattern));
        }
        
        query.where(predicates.toArray(new Predicate[0]));
        
        TypedQuery<TUser> typedQuery = entityManager.createQuery(query);
        List<TUser> users = typedQuery.getResultList();
        
        // 转换为响应DTO
        return users.stream()
                .map(user -> {
                    UserListResponse response = new UserListResponse();
                    response.setId(user.getId());
                    response.setUsername(user.getUsername());
                    response.setName(user.getName());
                    response.setAvatar(user.getAvatar());
                    response.setEmail(user.getEmail());
                    response.setPhone(user.getPhone());
                    response.setRole(user.getRole());
                    response.setDepartment(user.getDepartment());
                    response.setHeatUnitId(user.getHeatUnitId());
                    return response;
                })
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        try {
            // 查找用户
            Optional<TUser> optionalUser = userRepository.findById(userId);
            if (optionalUser.isEmpty()) {
                return false;
            }
            
            TUser user = optionalUser.get();
            
            // 验证旧密码
            if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
                return false;
            }
            
            // 设置新密码
            user.setPassword(passwordEncoder.encode(newPassword));
            user.setUpdateTime(LocalDateTime.now());
            
            // 保存用户信息
            userRepository.save(user);
            entityManager.flush();
            
            return true;
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return false;
        }
    }
}
